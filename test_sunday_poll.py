#!/usr/bin/env python3
"""
Test script to simulate Sunday poll posting.
This script temporarily modifies the date to test Sunday poll functionality.
"""

import sys
from pathlib import Path
from unittest.mock import patch
from datetime import datetime

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config import config
from src.utils.logger import get_logger
from src.core.enhanced_content_generator import EnhancedContentGenerator
from src.core.telegram_poster import TelegramPoster
from src.core.scheduler import ContentScheduler
from src.core.poll_poster import PollPoster

logger = get_logger("test_sunday_poll")


class TestSundayPoll:
    """Test class for Sunday poll functionality."""
    
    def __init__(self):
        """Initialize test components."""
        self.scheduler = ContentScheduler()
        self.enhanced_content_generator = EnhancedContentGenerator()
        self.telegram_poster = TelegramPoster()
        self.poll_poster = PollPoster()
    
    def test_sunday_detection(self):
        """Test Sunday detection logic."""
        print("=== TESTING SUNDAY DETECTION ===")
        
        # Test all days of the week
        days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        
        for i, day_name in enumerate(days):
            content_type = self.scheduler.get_content_type_for_day(i)
            is_sunday = (i == 6) or (content_type == "audience_question")
            
            print(f"{day_name} (day {i}): {content_type} - {'POLL DAY' if is_sunday else 'REGULAR CONTENT'}")
        
        print()
    
    def test_poll_generation(self):
        """Test poll generation."""
        print("=== TESTING POLL GENERATION ===")
        
        try:
            # Generate a test poll
            poll_data = self.poll_poster.poll_generator.generate_poll()
            
            print("✅ Poll generated successfully!")
            print(f"Question: {poll_data['question']}")
            print("Options:")
            for i, option in enumerate(poll_data['options'], 1):
                print(f"  {i}. {option}")
            
            # Test poll formatting
            preview_text = self.poll_poster._format_poll_as_text(poll_data)
            print(f"\n📝 Poll Preview Format:")
            print(preview_text)
            
            return True
            
        except Exception as e:
            print(f"❌ Poll generation failed: {str(e)}")
            return False
    
    def test_sunday_post_simulation(self):
        """Simulate Sunday post with poll."""
        print("=== SIMULATING SUNDAY POST ===")
        
        try:
            # Mock datetime to return Sunday (weekday 6)
            with patch('datetime.datetime') as mock_datetime:
                # Create a mock datetime that returns Sunday
                mock_datetime.now.return_value = datetime(2024, 1, 7)  # A Sunday
                mock_datetime.now().weekday.return_value = 6
                
                # Test content type detection
                content_type = self.scheduler.get_today_content_type()
                print(f"Detected content type: {content_type}")
                
                # Check if it would trigger poll logic
                is_poll_day = (mock_datetime.now().weekday() == 6) or (content_type == "audience_question")
                print(f"Would trigger poll logic: {is_poll_day}")
                
                if is_poll_day:
                    print("✅ Sunday poll logic would be triggered!")
                    
                    # Generate poll for testing
                    poll_data = self.poll_poster.poll_generator.generate_poll()
                    print(f"Poll question: {poll_data['question']}")
                    
                    return True
                else:
                    print("❌ Sunday poll logic would NOT be triggered")
                    return False
                    
        except Exception as e:
            print(f"❌ Sunday simulation failed: {str(e)}")
            return False
    
    def test_poll_to_user(self, user_id: int = 8034528128):
        """Test sending poll to specific user."""
        print(f"=== TESTING POLL TO USER {user_id} ===")
        
        try:
            # Send poll preview to user
            result = self.poll_poster.send_poll_preview_to_user(user_id)
            
            if result.get("success"):
                print(f"✅ Poll preview sent successfully to user {user_id}")
                return True
            else:
                error_msg = result.get("error", "Unknown error")
                print(f"❌ Failed to send poll preview: {error_msg}")
                return False
                
        except Exception as e:
            print(f"❌ Poll sending failed: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all tests."""
        print("🚀 STARTING SUNDAY POLL SYSTEM TESTS\n")
        
        tests = [
            ("Sunday Detection", self.test_sunday_detection),
            ("Poll Generation", self.test_poll_generation),
            ("Sunday Post Simulation", self.test_sunday_post_simulation),
            ("Poll to User", self.test_poll_to_user)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            print(f"RUNNING: {test_name}")
            print('='*50)
            
            try:
                result = test_func()
                results.append((test_name, result))
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"\n{test_name}: {status}")
            except Exception as e:
                results.append((test_name, False))
                print(f"\n{test_name}: ❌ ERROR - {str(e)}")
        
        # Summary
        print(f"\n{'='*50}")
        print("TEST SUMMARY")
        print('='*50)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Sunday poll system is ready!")
        else:
            print("⚠️  Some tests failed. Please check the issues above.")
        
        return passed == total


def main():
    """Main test function."""
    tester = TestSundayPoll()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
