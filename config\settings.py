"""
Application settings and configuration constants.
"""

# Telegram settings
TELEGRAM_MAX_MESSAGE_LENGTH = 4096
TELEGRAM_MAX_CAPTION_LENGTH = 1024

# Content generation settings
DEFAULT_CONTENT_LENGTH = 500
MAX_CONTENT_LENGTH = 1000

# API retry settings
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 5

# Logging settings
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# Content type mappings
CONTENT_TYPE_DESCRIPTIONS = {
    "cosmic_fact": "A surprising or fun space fact",
    "myth_buster": "Debunk a common space myth",
    "explainer": "Breakdown of a space concept in simple terms",
    "space_history": "A key moment or figure in space history",
    "cosmic_mystery": "An unsolved space question or phenomenon",
    "what_if_scenario": "A speculative space-related idea",
    "audience_question": "A thought-provoking question to spark comments"
}

# Weekly schedule
WEEKLY_CONTENT_SCHEDULE = {
    0: "cosmic_fact",      # Monday
    1: "myth_buster",      # Tuesday
    2: "explainer",        # Wednesday
    3: "space_history",    # Thursday
    4: "cosmic_mystery",   # Friday
    5: "what_if_scenario", # Saturday
    6: "audience_question" # Sunday
}
