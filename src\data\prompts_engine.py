"""
Advanced prompt engineering system for CosmicFacts content generation.
Implements sophisticated prompt engineering techniques for high-quality content.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime
import random


class BasePromptEngine(ABC):
    """Base class for all prompt engines with advanced prompt engineering techniques."""
    
    def __init__(self):
        """Initialize the base prompt engine."""
        self.content_type = self.__class__.__name__.lower().replace('promptengine', '')
        self.base_instructions = self._get_base_instructions()
        self.humanization_instructions = self._get_humanization_instructions()
    
    @abstractmethod
    def generate_stage1_prompt(self, topic: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 1 prompt for raw content creation."""
        pass
    
    @abstractmethod
    def generate_stage2_prompt(self, raw_content: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 2 prompt for content humanization and enhancement."""
        pass
    
    def _get_base_instructions(self) -> str:
        """Get base instructions for content generation."""
        return """
CORE CONTENT GENERATION PRINCIPLES:
1. ACCURACY: All scientific information must be factually correct and up-to-date
2. ENGAGEMENT: Content should be fascinating and capture reader attention immediately
3. CLARITY: Complex concepts explained in accessible language
4. STRUCTURE: Well-organized with clear flow and logical progression
5. UNIQUENESS: Fresh perspective on topics, avoiding clichéd presentations
6. EDUCATIONAL VALUE: Readers should learn something meaningful and memorable

TECHNICAL REQUIREMENTS:
- Respond ONLY with valid JSON format
- Use simple HTML tags: <b>, <i>, <u>, <code> (NO document structure tags)
- Content length: 400-800 characters for optimal Telegram readability
- Include 3-5 relevant hashtags for social media engagement
- Maintain scientific accuracy while being accessible to general audience
"""
    
    def _get_humanization_instructions(self) -> str:
        """Get instructions for content humanization."""
        return """
HUMANIZATION TECHNIQUES:
1. CONVERSATIONAL TONE: Write as if explaining to a curious friend
2. EMOTIONAL CONNECTION: Add wonder, excitement, or surprise elements
3. RELATABLE ANALOGIES: Use everyday comparisons to explain complex concepts
4. STORYTELLING: Frame facts within narrative context when appropriate
5. PERSONAL TOUCH: Use inclusive language ("we", "our universe", "imagine")
6. NATURAL FLOW: Ensure smooth transitions and natural reading rhythm
7. CURIOSITY HOOKS: End with thought-provoking elements or questions

STYLE GUIDELINES:
- Avoid robotic or overly formal language
- Use varied sentence structures and lengths
- Include rhetorical questions or exclamations when appropriate
- Add personality while maintaining scientific credibility
- Make content shareable and discussion-worthy
"""
    
    def _add_context_modifiers(self, base_prompt: str, context: Dict[str, Any] = None) -> str:
        """Add context-specific modifiers to the prompt."""
        if not context:
            context = {}
        
        modifiers = []
        
        # Time-based modifiers
        current_hour = datetime.now().hour
        if 6 <= current_hour < 12:
            modifiers.append("Add a fresh, morning energy to the content.")
        elif 12 <= current_hour < 18:
            modifiers.append("Make the content engaging for afternoon readers.")
        elif 18 <= current_hour < 22:
            modifiers.append("Create content perfect for evening discovery.")
        else:
            modifiers.append("Craft content that sparks late-night wonder.")
        
        # Audience modifiers
        if context.get('target_audience') == 'young':
            modifiers.append("Use more dynamic language and exciting discoveries.")
        elif context.get('target_audience') == 'academic':
            modifiers.append("Include more technical details while remaining accessible.")
        
        # Seasonal modifiers
        month = datetime.now().month
        if month in [12, 1, 2]:
            modifiers.append("Consider winter sky observations or seasonal astronomical events.")
        elif month in [6, 7, 8]:
            modifiers.append("Reference summer stargazing or seasonal phenomena.")
        
        if modifiers:
            modifier_text = "\n\nCONTEXT MODIFIERS:\n" + "\n".join(f"- {mod}" for mod in modifiers)
            return base_prompt + modifier_text
        
        return base_prompt


class CosmicFactPromptEngine(BasePromptEngine):
    """Advanced prompt engine for cosmic facts with sophisticated techniques."""
    
    def generate_stage1_prompt(self, topic: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 1 prompt for cosmic fact creation."""
        
        fact_angles = [
            "mind-blowing scale and measurements",
            "recent groundbreaking discoveries",
            "counterintuitive properties that challenge common sense",
            "connections to everyday life and human experience",
            "historical significance and discovery story",
            "future implications and ongoing research"
        ]
        
        selected_angle = random.choice(fact_angles)
        
        base_prompt = f"""
{self.base_instructions}

CONTENT TYPE: Cosmic Fact
TOPIC: {topic}
APPROACH ANGLE: Focus on {selected_angle}

COSMIC FACT SPECIFIC GUIDELINES:
1. START WITH IMPACT: Open with the most surprising or impressive aspect
2. SCALE EMPHASIS: Help readers grasp enormous scales through comparisons
3. VISUAL IMAGERY: Paint vivid mental pictures of cosmic phenomena
4. PRECISION: Include specific numbers, distances, or measurements when impactful
5. WONDER FACTOR: Emphasize what makes this fact truly extraordinary

CONTENT STRUCTURE:
- Hook: Attention-grabbing opening statement
- Core Fact: The main surprising information
- Context: Why this matters or what it means
- Impact: How this relates to our understanding of the universe

JSON FORMAT REQUIRED:
{{
    "title": "Captivating title that hints at the surprise",
    "content": "Main content with the cosmic fact and context",
    "hashtags": ["#space", "#astronomy", "#cosmicfacts", "#universe", "#science"]
}}

EXAMPLE OPENING STYLES:
- "Did you know that..."
- "In just one second..."
- "While you're reading this..."
- "Imagine if..."
- "Scientists recently discovered..."

Generate content about: {topic}
Focus on making it absolutely fascinating and memorable!
"""
        
        return self._add_context_modifiers(base_prompt, context)
    
    def generate_stage2_prompt(self, raw_content: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 2 prompt for cosmic fact humanization."""
        
        humanization_techniques = [
            "Add personal wonder and excitement",
            "Include relatable analogies and comparisons",
            "Enhance emotional connection to the cosmos",
            "Improve conversational flow and readability",
            "Strengthen the 'wow factor' and shareability"
        ]
        
        selected_technique = random.choice(humanization_techniques)
        
        prompt = f"""
{self.humanization_instructions}

STAGE 2: CONTENT HUMANIZATION AND ENHANCEMENT
PRIMARY FOCUS: {selected_technique}

ORIGINAL CONTENT TO ENHANCE:
{raw_content}

HUMANIZATION OBJECTIVES:
1. EMOTIONAL RESONANCE: Make readers feel connected to the cosmic wonder
2. CONVERSATIONAL FLOW: Transform formal facts into engaging storytelling
3. RELATABILITY: Add analogies that help readers grasp the scale and significance
4. PERSONALITY: Inject enthusiasm and curiosity into the writing
5. SHAREABILITY: Make it irresistible to share with friends

ENHANCEMENT TECHNIQUES:
- Replace passive voice with active, dynamic language
- Add transitional phrases that create smooth flow
- Include rhetorical questions that engage the reader
- Use sensory language to create vivid mental images
- Add emotional markers (amazing, incredible, mind-blowing - but use sparingly)

MAINTAIN:
- All factual accuracy and scientific precision
- The core message and key information
- Appropriate length for Telegram (400-800 characters)
- HTML formatting and hashtag structure

JSON FORMAT REQUIRED:
{{
    "title": "Enhanced, more engaging title",
    "content": "Humanized and enhanced content with natural flow",
    "hashtags": ["#space", "#astronomy", "#cosmicfacts", "#universe", "#science"]
}}

Transform the content to be more human, engaging, and emotionally resonant while maintaining scientific accuracy!
"""

        return self._add_context_modifiers(prompt, context)


class MythBusterPromptEngine(BasePromptEngine):
    """Advanced prompt engine for space myth-busting content."""

    def generate_stage1_prompt(self, topic: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 1 prompt for myth-busting content."""

        debunking_approaches = [
            "scientific evidence and research findings",
            "logical reasoning and critical thinking",
            "historical context and origin of the myth",
            "expert testimonials and authoritative sources",
            "practical demonstrations and thought experiments"
        ]

        selected_approach = random.choice(debunking_approaches)

        base_prompt = f"""
{self.base_instructions}

CONTENT TYPE: Space Myth-Buster
TOPIC/MYTH: {topic}
DEBUNKING APPROACH: Use {selected_approach}

MYTH-BUSTING SPECIFIC GUIDELINES:
1. RESPECTFUL DEBUNKING: Address misconceptions without condescending tone
2. CLEAR CONTRAST: Clearly separate myth from reality
3. EXPLANATION: Explain WHY the myth exists and how it spread
4. EVIDENCE: Provide concrete scientific evidence for the truth
5. EDUCATIONAL: Help readers develop critical thinking skills

CONTENT STRUCTURE:
- Myth Statement: What people commonly believe
- Reality Check: What science actually tells us
- Evidence: Supporting facts and research
- Explanation: Why the myth persists and how to think critically

JSON FORMAT REQUIRED:
{{
    "title": "Myth vs Reality: [Clear, engaging title]",
    "content": "Myth-busting content with evidence and explanation",
    "hashtags": ["#mythbuster", "#spacefacts", "#science", "#astronomy", "#truth"]
}}

EFFECTIVE OPENING PATTERNS:
- "Many people believe that... but science shows..."
- "It's a common misconception that..."
- "Hollywood has convinced us that... however..."
- "You might have heard that... let's examine the facts..."

Address this myth: {topic}
Make it educational and empowering for critical thinking!
"""

        return self._add_context_modifiers(base_prompt, context)

    def generate_stage2_prompt(self, raw_content: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 2 prompt for myth-buster humanization."""

        prompt = f"""
{self.humanization_instructions}

STAGE 2: MYTH-BUSTER CONTENT HUMANIZATION
FOCUS: Making scientific debunking engaging and non-confrontational

ORIGINAL CONTENT TO ENHANCE:
{raw_content}

MYTH-BUSTER HUMANIZATION OBJECTIVES:
1. FRIENDLY TONE: Avoid making readers feel foolish for believing myths
2. CURIOSITY BUILDING: Frame debunking as exciting discovery
3. RELATABILITY: Acknowledge why myths seem believable
4. EMPOWERMENT: Help readers feel smarter and more informed
5. ENGAGEMENT: Make fact-checking feel like detective work

ENHANCEMENT TECHNIQUES:
- Use inclusive language ("we've all heard", "it's natural to think")
- Frame corrections as discoveries rather than corrections
- Add intrigue and mystery elements to the debunking process
- Include "aha moment" revelations
- Make readers feel like they're part of the scientific community

JSON FORMAT REQUIRED:
{{
    "title": "Enhanced myth-busting title with intrigue",
    "content": "Humanized debunking content that's engaging and educational",
    "hashtags": ["#mythbuster", "#spacefacts", "#science", "#astronomy", "#truth"]
}}

Transform the myth-busting into an engaging, friendly learning experience!
"""

        return self._add_context_modifiers(prompt, context)


class ExplainerPromptEngine(BasePromptEngine):
    """Advanced prompt engine for complex concept explanations."""

    def generate_stage1_prompt(self, topic: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 1 prompt for explainer content."""

        explanation_methods = [
            "step-by-step breakdown with analogies",
            "building from familiar concepts to complex ones",
            "visual metaphors and mental models",
            "cause-and-effect relationships",
            "historical development and discovery process"
        ]

        selected_method = random.choice(explanation_methods)

        base_prompt = f"""
{self.base_instructions}

CONTENT TYPE: Space Concept Explainer
TOPIC: {topic}
EXPLANATION METHOD: {selected_method}

EXPLAINER SPECIFIC GUIDELINES:
1. ACCESSIBILITY: Make complex concepts understandable to general audience
2. PROGRESSIVE BUILDING: Start simple, gradually add complexity
3. ANALOGIES: Use familiar comparisons to explain unfamiliar concepts
4. CLARITY: Avoid jargon, define necessary technical terms
5. ENGAGEMENT: Keep readers interested throughout the explanation

CONTENT STRUCTURE:
- Hook: Why this concept matters or is interesting
- Foundation: Basic principles or starting point
- Development: Step-by-step explanation
- Significance: Why understanding this matters

JSON FORMAT REQUIRED:
{{
    "title": "Understanding [Concept]: Simple Explanation",
    "content": "Clear, accessible explanation with analogies",
    "hashtags": ["#spaceexplained", "#astronomy", "#science", "#learning", "#cosmos"]
}}

EFFECTIVE EXPLANATION STARTERS:
- "Imagine if..."
- "Think of it like..."
- "To understand this, let's start with..."
- "Picture this scenario..."
- "Here's a simple way to think about it..."

Explain this concept clearly: {topic}
Make it accessible and fascinating for everyone!
"""

        return self._add_context_modifiers(base_prompt, context)

    def generate_stage2_prompt(self, raw_content: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 2 prompt for explainer humanization."""

        prompt = f"""
{self.humanization_instructions}

STAGE 2: EXPLAINER CONTENT HUMANIZATION
FOCUS: Making complex explanations feel like friendly teaching

ORIGINAL CONTENT TO ENHANCE:
{raw_content}

EXPLAINER HUMANIZATION OBJECTIVES:
1. TEACHER PERSONA: Write like a passionate, patient teacher
2. ENCOURAGEMENT: Make readers feel capable of understanding
3. WONDER: Maintain sense of amazement throughout explanation
4. INTERACTION: Use questions and prompts to engage thinking
5. CONFIDENCE: Help readers feel smarter after reading

ENHANCEMENT TECHNIQUES:
- Add encouraging phrases ("you're getting it!", "pretty cool, right?")
- Use second person to directly engage readers
- Include moments of revelation and understanding
- Add enthusiasm for the subject matter
- Create a sense of shared discovery

JSON FORMAT REQUIRED:
{{
    "title": "Enhanced explainer title that invites learning",
    "content": "Humanized explanation that feels like friendly teaching",
    "hashtags": ["#spaceexplained", "#astronomy", "#science", "#learning", "#cosmos"]
}}

Transform the explanation into an engaging, encouraging learning experience!
"""

        return self._add_context_modifiers(prompt, context)


class SpaceHistoryPromptEngine(BasePromptEngine):
    """Advanced prompt engine for space history content."""

    def generate_stage1_prompt(self, topic: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 1 prompt for space history content."""

        historical_angles = [
            "human drama and personal stories behind discoveries",
            "technological breakthroughs and engineering challenges",
            "political and social context of space achievements",
            "unexpected discoveries and serendipitous moments",
            "international cooperation and competition"
        ]

        selected_angle = random.choice(historical_angles)

        base_prompt = f"""
{self.base_instructions}

CONTENT TYPE: Space History
TOPIC: {topic}
HISTORICAL ANGLE: Focus on {selected_angle}

SPACE HISTORY SPECIFIC GUIDELINES:
1. STORYTELLING: Present history as compelling narrative
2. HUMAN ELEMENT: Highlight the people behind the achievements
3. CONTEXT: Explain the significance within historical period
4. DRAMA: Include challenges, failures, and triumphs
5. LEGACY: Connect historical events to modern space exploration

CONTENT STRUCTURE:
- Setting: Historical context and circumstances
- Challenge: What problem or goal was being addressed
- Journey: The process, struggles, and breakthroughs
- Impact: How this changed space exploration or our understanding

JSON FORMAT REQUIRED:
{{
    "title": "Historic Moment: [Engaging historical title]",
    "content": "Compelling historical narrative with human drama",
    "hashtags": ["#spacehistory", "#astronomy", "#exploration", "#science", "#discovery"]
}}

ENGAGING HISTORICAL OPENINGS:
- "On [date], everything changed when..."
- "Behind the headlines of [event] was an incredible story..."
- "Few people know that [achievement] almost didn't happen..."
- "The year was [year], and scientists faced an impossible challenge..."

Tell this historical story: {topic}
Make it dramatic and inspiring!
"""

        return self._add_context_modifiers(base_prompt, context)

    def generate_stage2_prompt(self, raw_content: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 2 prompt for space history humanization."""

        prompt = f"""
{self.humanization_instructions}

STAGE 2: SPACE HISTORY HUMANIZATION
FOCUS: Making historical events feel immediate and personal

ORIGINAL CONTENT TO ENHANCE:
{raw_content}

HISTORY HUMANIZATION OBJECTIVES:
1. TIME TRAVEL: Make readers feel present during historical events
2. EMOTIONAL CONNECTION: Highlight human emotions and motivations
3. RELATABILITY: Connect historical figures to modern readers
4. INSPIRATION: Show how ordinary people achieved extraordinary things
5. RELEVANCE: Link historical achievements to current space exploration

ENHANCEMENT TECHNIQUES:
- Use present tense for dramatic moments
- Include sensory details and emotional reactions
- Add dialogue or quotes when appropriate
- Create suspense and dramatic tension
- Show the human cost and dedication behind achievements

JSON FORMAT REQUIRED:
{{
    "title": "Enhanced historical title with emotional appeal",
    "content": "Humanized historical narrative that feels immediate",
    "hashtags": ["#spacehistory", "#astronomy", "#exploration", "#science", "#discovery"]
}}

Transform the history into a compelling, emotional story!
"""

        return self._add_context_modifiers(prompt, context)


class CosmicMysteryPromptEngine(BasePromptEngine):
    """Advanced prompt engine for cosmic mysteries and unsolved questions."""

    def generate_stage1_prompt(self, topic: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 1 prompt for cosmic mystery content."""

        mystery_approaches = [
            "detective-style investigation of clues and evidence",
            "competing theories and scientific debates",
            "cutting-edge research and latest findings",
            "philosophical implications and big questions",
            "technological challenges in solving the mystery"
        ]

        selected_approach = random.choice(mystery_approaches)

        base_prompt = f"""
{self.base_instructions}

CONTENT TYPE: Cosmic Mystery
TOPIC: {topic}
MYSTERY APPROACH: {selected_approach}

COSMIC MYSTERY SPECIFIC GUIDELINES:
1. INTRIGUE: Build suspense and curiosity about the unknown
2. EVIDENCE: Present what we know and what puzzles remain
3. THEORIES: Explore different scientific explanations
4. IMPLICATIONS: Discuss what solving this mystery could mean
5. WONDER: Maintain sense of awe about cosmic unknowns

CONTENT STRUCTURE:
- Mystery Setup: What we don't understand and why it matters
- Current Knowledge: What evidence and clues we have
- Theories: Different scientific explanations being explored
- Future: How we might solve this mystery

JSON FORMAT REQUIRED:
{{
    "title": "Cosmic Mystery: [Intriguing question or phenomenon]",
    "content": "Mysterious content that explores the unknown",
    "hashtags": ["#cosmicmystery", "#astronomy", "#science", "#universe", "#unknown"]
}}

MYSTERY-BUILDING OPENINGS:
- "Deep in space, something strange is happening..."
- "Scientists are baffled by..."
- "One of the universe's greatest puzzles is..."
- "We thought we understood [topic], but then..."

Explore this cosmic mystery: {topic}
Make it intriguing and thought-provoking!
"""

        return self._add_context_modifiers(base_prompt, context)

    def generate_stage2_prompt(self, raw_content: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 2 prompt for cosmic mystery humanization."""

        prompt = f"""
{self.humanization_instructions}

STAGE 2: COSMIC MYSTERY HUMANIZATION
FOCUS: Making scientific mysteries feel like thrilling investigations

ORIGINAL CONTENT TO ENHANCE:
{raw_content}

MYSTERY HUMANIZATION OBJECTIVES:
1. DETECTIVE WORK: Frame science as exciting investigation
2. SUSPENSE: Build tension and curiosity throughout
3. PARTICIPATION: Make readers feel like part of the investigation
4. WONDER: Emphasize the thrill of exploring the unknown
5. HOPE: Suggest that mysteries can be solved with persistence

ENHANCEMENT TECHNIQUES:
- Use mystery and detective language
- Create cliffhangers and revelations
- Add scientist quotes and personal stakes
- Include "what if" scenarios
- Build anticipation for future discoveries

JSON FORMAT REQUIRED:
{{
    "title": "Enhanced mystery title with intrigue and suspense",
    "content": "Humanized mystery content that feels like a thriller",
    "hashtags": ["#cosmicmystery", "#astronomy", "#science", "#universe", "#unknown"]
}}

Transform the mystery into a thrilling scientific investigation!
"""

        return self._add_context_modifiers(prompt, context)


class WhatIfScenarioPromptEngine(BasePromptEngine):
    """Advanced prompt engine for speculative 'what if' scenarios."""

    def generate_stage1_prompt(self, topic: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 1 prompt for what-if scenario content."""

        scenario_types = [
            "alternative physics or cosmic conditions",
            "technological advancement scenarios",
            "discovery and contact scenarios",
            "environmental and planetary changes",
            "time-based hypothetical situations"
        ]

        selected_type = random.choice(scenario_types)

        base_prompt = f"""
{self.base_instructions}

CONTENT TYPE: What-If Scenario
TOPIC: {topic}
SCENARIO TYPE: {selected_type}

WHAT-IF SPECIFIC GUIDELINES:
1. SCIENTIFIC BASIS: Ground speculation in real science
2. LOGICAL PROGRESSION: Show cause-and-effect relationships
3. IMAGINATION: Encourage creative thinking about possibilities
4. PLAUSIBILITY: Keep scenarios within realm of scientific possibility
5. IMPLICATIONS: Explore consequences and ramifications

CONTENT STRUCTURE:
- Scenario Setup: The hypothetical situation or change
- Scientific Basis: Why this could theoretically happen
- Consequences: What would result from this scenario
- Implications: How it would affect life, Earth, or the universe

JSON FORMAT REQUIRED:
{{
    "title": "What If: [Intriguing hypothetical question]",
    "content": "Speculative content exploring fascinating possibilities",
    "hashtags": ["#whatif", "#space", "#science", "#speculation", "#cosmos"]
}}

ENGAGING SCENARIO STARTERS:
- "Imagine if suddenly..."
- "What would happen if..."
- "Picture a universe where..."
- "Suppose that tomorrow..."
- "In an alternate reality where..."

Explore this scenario: {topic}
Make it thought-provoking and scientifically grounded!
"""

        return self._add_context_modifiers(base_prompt, context)

    def generate_stage2_prompt(self, raw_content: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 2 prompt for what-if scenario humanization."""

        prompt = f"""
{self.humanization_instructions}

STAGE 2: WHAT-IF SCENARIO HUMANIZATION
FOCUS: Making speculative content feel personally relevant and exciting

ORIGINAL CONTENT TO ENHANCE:
{raw_content}

SCENARIO HUMANIZATION OBJECTIVES:
1. PERSONAL STAKES: Help readers imagine how scenarios affect them
2. VIVID IMAGERY: Create clear mental pictures of hypothetical worlds
3. EMOTIONAL ENGAGEMENT: Make readers care about the outcomes
4. INTERACTIVE THINKING: Encourage readers to explore implications
5. WONDER: Maintain sense of possibility and excitement

ENHANCEMENT TECHNIQUES:
- Use second person to involve readers directly
- Add sensory details to make scenarios vivid
- Include emotional reactions and human responses
- Create "you are there" moments
- Encourage readers to continue the thought experiment

JSON FORMAT REQUIRED:
{{
    "title": "Enhanced what-if title that invites imagination",
    "content": "Humanized scenario that feels personally engaging",
    "hashtags": ["#whatif", "#space", "#science", "#speculation", "#cosmos"]
}}

Transform the scenario into an immersive thought experiment!
"""

        return self._add_context_modifiers(prompt, context)


class AudienceQuestionPromptEngine(BasePromptEngine):
    """Advanced prompt engine for audience engagement questions."""

    def generate_stage1_prompt(self, topic: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 1 prompt for audience question content."""

        question_styles = [
            "philosophical and thought-provoking questions",
            "personal preference and opinion questions",
            "hypothetical choice scenarios",
            "prediction and speculation questions",
            "ethical and moral dilemma questions"
        ]

        selected_style = random.choice(question_styles)

        base_prompt = f"""
{self.base_instructions}

CONTENT TYPE: Audience Question
TOPIC: {topic}
QUESTION STYLE: {selected_style}

AUDIENCE QUESTION SPECIFIC GUIDELINES:
1. ENGAGEMENT: Create questions that spark discussion and comments
2. ACCESSIBILITY: Make questions approachable for all knowledge levels
3. RELEVANCE: Connect space topics to human experience and values
4. VARIETY: Offer different perspectives and viewpoints to consider
5. SHAREABILITY: Make content that people want to discuss with others

CONTENT STRUCTURE:
- Context: Brief background on the topic
- Question: The main thought-provoking question
- Perspectives: Different angles or considerations
- Invitation: Encourage audience participation

JSON FORMAT REQUIRED:
{{
    "title": "Question: [Engaging question that invites response]",
    "content": "Question content that encourages discussion",
    "hashtags": ["#spacequestion", "#astronomy", "#discussion", "#thoughts", "#cosmos"]
}}

EFFECTIVE QUESTION PATTERNS:
- "If you could [scenario], would you...?"
- "What do you think would happen if...?"
- "Which would you choose: [option A] or [option B]?"
- "Do you believe that...?"
- "How would you feel if...?"

Create an engaging question about: {topic}
Make it irresistible to answer and discuss!
"""

        return self._add_context_modifiers(base_prompt, context)

    def generate_stage2_prompt(self, raw_content: str, context: Dict[str, Any] = None) -> str:
        """Generate Stage 2 prompt for audience question humanization."""

        prompt = f"""
{self.humanization_instructions}

STAGE 2: AUDIENCE QUESTION HUMANIZATION
FOCUS: Making questions feel like friendly conversation starters

ORIGINAL CONTENT TO ENHANCE:
{raw_content}

QUESTION HUMANIZATION OBJECTIVES:
1. CONVERSATIONAL: Make questions feel like chatting with friends
2. INCLUSIVE: Ensure everyone feels qualified to participate
3. CURIOSITY: Spark genuine interest and wonder
4. COMMUNITY: Create sense of shared exploration
5. RESPECT: Value all perspectives and opinions

ENHANCEMENT TECHNIQUES:
- Use warm, inviting language
- Add personal touches and relatability
- Include multiple entry points for different comfort levels
- Show genuine curiosity about audience thoughts
- Create safe space for sharing opinions

JSON FORMAT REQUIRED:
{{
    "title": "Enhanced question title that feels welcoming",
    "content": "Humanized question that invites friendly discussion",
    "hashtags": ["#spacequestion", "#astronomy", "#discussion", "#thoughts", "#cosmos"]
}}

Transform the question into a warm conversation starter!
"""

        return self._add_context_modifiers(prompt, context)


# Prompt Engine Factory
class PromptEngineFactory:
    """Factory class for creating appropriate prompt engines."""

    _engines = {
        'cosmic_fact': CosmicFactPromptEngine,
        'myth_buster': MythBusterPromptEngine,
        'explainer': ExplainerPromptEngine,
        'space_history': SpaceHistoryPromptEngine,
        'cosmic_mystery': CosmicMysteryPromptEngine,
        'what_if_scenario': WhatIfScenarioPromptEngine,
        'audience_question': AudienceQuestionPromptEngine
    }

    @classmethod
    def get_engine(cls, content_type: str) -> BasePromptEngine:
        """Get the appropriate prompt engine for the content type."""
        engine_class = cls._engines.get(content_type)
        if not engine_class:
            raise ValueError(f"Unknown content type: {content_type}")
        return engine_class()

    @classmethod
    def get_available_types(cls) -> List[str]:
        """Get list of available content types."""
        return list(cls._engines.keys())
