"""
Unit tests for the formatters module.
"""

import pytest
from src.utils.formatters import TelegramFormatter, format_for_telegram


class TestTelegramFormatter:
    """Test cases for TelegramFormatter class."""
    
    @pytest.fixture
    def formatter(self):
        """Create a TelegramFormatter instance for testing."""
        return TelegramFormatter()
    
    def test_format_content_with_dict(self, formatter):
        """Test formatting content from dictionary."""
        content_data = {
            "title": "Test Title",
            "content": "Test content with <b>bold</b> text",
            "hashtags": ["#space", "#test"]
        }
        
        result = formatter.format_content(content_data)
        
        assert "Test Title" in result
        assert "Test content" in result
        assert "#space #test" in result
        assert "<b>" in result
    
    def test_format_content_with_json_string(self, formatter):
        """Test formatting content from JSON string."""
        json_content = '{"title": "JSON Title", "content": "JSON content"}'
        
        result = formatter.format_content(json_content)
        
        assert "JSON Title" in result
        assert "JSON content" in result
    
    def test_parse_json_content_valid(self, formatter):
        """Test parsing valid JSON content."""
        json_string = '{"title": "Test", "content": "Content"}'
        
        result = formatter._parse_json_content(json_string)
        
        assert result["title"] == "Test"
        assert result["content"] == "Content"
    
    def test_parse_json_content_with_markdown(self, formatter):
        """Test parsing JSON content with markdown code blocks."""
        json_with_markdown = '```json\n{"title": "Test"}\n```'
        
        result = formatter._parse_json_content(json_with_markdown)
        
        assert result["title"] == "Test"
    
    def test_parse_json_content_invalid(self, formatter):
        """Test parsing invalid JSON content."""
        invalid_json = "Not JSON content"
        
        result = formatter._parse_json_content(invalid_json)
        
        assert "content" in result
        assert result["content"] == invalid_json
    
    def test_build_message_complete(self, formatter):
        """Test building message with all components."""
        content_data = {
            "title": "Test Title",
            "content": "Main content",
            "explanation": "Additional explanation",
            "hashtags": ["#tag1", "#tag2"]
        }
        
        result = formatter._build_message(content_data)
        
        assert "<b>Test Title</b>" in result
        assert "Main content" in result
        assert "Additional explanation" in result
        assert "#tag1 #tag2" in result
    
    def test_truncate_if_needed_short_message(self, formatter):
        """Test truncation with short message."""
        short_message = "Short message"
        
        result = formatter._truncate_if_needed(short_message)
        
        assert result == short_message
    
    def test_truncate_if_needed_long_message(self, formatter):
        """Test truncation with long message."""
        long_message = "x" * 5000  # Exceeds Telegram limit
        
        result = formatter._truncate_if_needed(long_message)
        
        assert len(result) <= formatter.MAX_MESSAGE_LENGTH
        assert result.endswith("...")
    
    def test_clean_html_formatting(self, formatter):
        """Test HTML formatting cleanup."""
        message_with_markdown = "**bold** *italic* `code`"
        
        result = formatter._clean_html_formatting(message_with_markdown)
        
        assert "<b>bold</b>" in result
        assert "<i>italic</i>" in result
        assert "<code>code</code>" in result
    
    def test_format_for_telegram_function(self):
        """Test the convenience function."""
        content = {"title": "Test", "content": "Content"}
        
        result = format_for_telegram(content)
        
        assert "Test" in result
        assert "Content" in result
