"""
Content scheduling module for determining daily content types and prompts.
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ContentScheduler:
    """Content scheduler class for managing daily content types and prompts."""
    
    # Weekly content schedule mapping
    WEEKLY_SCHEDULE = {
        0: "cosmic_fact",      # Monday
        1: "myth_buster",      # Tuesday
        2: "explainer",        # Wednesday
        3: "space_history",    # Thursday
        4: "cosmic_mystery",   # Friday
        5: "what_if_scenario", # Saturday
        6: "audience_question" # Sunday
    }
    
    def __init__(self, prompts_dir: str = "src/data/prompts"):
        """
        Initialize the content scheduler.
        
        Args:
            prompts_dir: Directory containing prompt JSON files
        """
        self.prompts_dir = Path(prompts_dir)
        self._ensure_prompts_directory()
        self._create_default_prompts()
        logger.info("Content scheduler initialized")
    
    def get_today_content_type(self) -> str:
        """
        Get the content type for today based on the weekly schedule.
        
        Returns:
            Content type string for today
        """
        today = datetime.now().weekday()
        content_type = self.WEEKLY_SCHEDULE.get(today, "cosmic_fact")
        logger.info(f"Today's content type: {content_type}")
        return content_type
    
    def get_content_type_for_day(self, day_of_week: int) -> str:
        """
        Get the content type for a specific day of the week.
        
        Args:
            day_of_week: Day of week (0=Monday, 6=Sunday)
            
        Returns:
            Content type string for the specified day
        """
        return self.WEEKLY_SCHEDULE.get(day_of_week, "cosmic_fact")
    
    def get_prompt_for_content_type(self, content_type: str) -> Dict[str, Any]:
        """
        Get the prompt configuration for a specific content type.
        
        Args:
            content_type: Type of content to get prompt for
            
        Returns:
            Prompt configuration dictionary
        """
        try:
            prompt_file = self.prompts_dir / f"{content_type}.json"
            
            if not prompt_file.exists():
                logger.warning(f"Prompt file not found: {prompt_file}")
                return self._get_default_prompt(content_type)
            
            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt_data = json.load(f)
            
            logger.info(f"Loaded prompt for {content_type}")
            return prompt_data
            
        except Exception as e:
            logger.error(f"Error loading prompt for {content_type}: {str(e)}")
            return self._get_default_prompt(content_type)
    
    def get_today_prompt(self) -> Dict[str, Any]:
        """
        Get the prompt configuration for today's content type.
        
        Returns:
            Today's prompt configuration dictionary
        """
        content_type = self.get_today_content_type()
        return self.get_prompt_for_content_type(content_type)
    
    def _ensure_prompts_directory(self) -> None:
        """Ensure the prompts directory exists."""
        self.prompts_dir.mkdir(parents=True, exist_ok=True)
        logger.debug(f"Prompts directory ensured: {self.prompts_dir}")
    
    def _create_default_prompts(self) -> None:
        """Create default prompt files if they don't exist."""
        default_prompts = {
            "cosmic_fact": {
                "content_type": "cosmic_fact",
                "description": "A surprising or fun space fact",
                "prompt": "Generate an amazing and surprising cosmic fact that will fascinate readers. Include interesting details and context that make the fact memorable. Focus on recent discoveries or lesser-known phenomena in space.",
                "example_topics": [
                    "Black holes and their properties",
                    "Exoplanets and their characteristics",
                    "Stellar phenomena and lifecycle",
                    "Galaxy formation and structure",
                    "Cosmic distances and scales"
                ]
            },
            "myth_buster": {
                "content_type": "myth_buster",
                "description": "Debunk a common space myth",
                "prompt": "Debunk a common space myth or misconception. Explain what people commonly believe, why it's incorrect, and provide the accurate scientific explanation. Make it educational and engaging.",
                "example_topics": [
                    "Moon landing conspiracies",
                    "Asteroid belt dangers",
                    "Vacuum of space effects",
                    "Planet alignment effects",
                    "Solar system misconceptions"
                ]
            },
            "explainer": {
                "content_type": "explainer",
                "description": "Breakdown of a space concept in simple terms",
                "prompt": "Explain a complex space concept in simple, easy-to-understand terms. Use analogies and examples that relate to everyday life. Make it accessible to people without scientific background.",
                "example_topics": [
                    "How gravity works",
                    "What is dark matter",
                    "How stars form",
                    "What are gravitational waves",
                    "How do rockets work"
                ]
            },
            "space_history": {
                "content_type": "space_history",
                "description": "A key moment or figure in space history",
                "prompt": "Share a fascinating moment or important figure from space exploration history. Include interesting details and explain why this event or person was significant for space exploration.",
                "example_topics": [
                    "First human in space",
                    "Moon landing achievements",
                    "Space station milestones",
                    "Robotic mission successes",
                    "Space exploration pioneers"
                ]
            },
            "cosmic_mystery": {
                "content_type": "cosmic_mystery",
                "description": "An unsolved space question or phenomenon",
                "prompt": "Present an intriguing cosmic mystery or unsolved question in astronomy. Explain what we know, what we don't know, and why it's important to solve this mystery.",
                "example_topics": [
                    "Dark energy nature",
                    "Fast radio bursts",
                    "Missing matter problem",
                    "Fermi paradox",
                    "Cosmic ray origins"
                ]
            },
            "what_if_scenario": {
                "content_type": "what_if_scenario",
                "description": "A speculative space-related idea",
                "prompt": "Present an interesting 'what if' scenario related to space. Explore the scientific possibilities and implications. Make it thought-provoking and based on real science.",
                "example_topics": [
                    "What if Earth had rings",
                    "What if we could travel at light speed",
                    "What if the Moon was closer",
                    "What if we found alien life",
                    "What if black holes didn't exist"
                ]
            },
            "audience_question": {
                "content_type": "audience_question",
                "description": "A thought-provoking question to spark comments",
                "prompt": "Create a thought-provoking question about space that will encourage audience engagement and discussion. Include some context to help people think about the topic.",
                "example_topics": [
                    "Space colonization ethics",
                    "Alien contact scenarios",
                    "Space exploration priorities",
                    "Future technology possibilities",
                    "Cosmic perspective questions"
                ]
            }
        }
        
        for content_type, prompt_data in default_prompts.items():
            prompt_file = self.prompts_dir / f"{content_type}.json"
            
            if not prompt_file.exists():
                with open(prompt_file, 'w', encoding='utf-8') as f:
                    json.dump(prompt_data, f, indent=2, ensure_ascii=False)
                logger.info(f"Created default prompt file: {prompt_file}")
    
    def _get_default_prompt(self, content_type: str) -> Dict[str, Any]:
        """
        Get a basic default prompt for any content type.
        
        Args:
            content_type: Type of content
            
        Returns:
            Default prompt configuration
        """
        return {
            "content_type": content_type,
            "description": f"Content about {content_type.replace('_', ' ')}",
            "prompt": f"Generate engaging content about {content_type.replace('_', ' ')} related to space and astronomy.",
            "example_topics": ["General space topics"]
        }
