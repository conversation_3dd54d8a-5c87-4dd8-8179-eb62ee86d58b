"""
Unit tests for the content generator module.
"""

import pytest
import json
from unittest.mock import Mock, patch
from src.core.content_generator import ContentGenerator


class TestContentGenerator:
    """Test cases for ContentGenerator class."""
    
    @pytest.fixture
    def content_generator(self):
        """Create a ContentGenerator instance for testing."""
        with patch('src.core.content_generator.genai.configure'):
            with patch('src.core.content_generator.genai.GenerativeModel'):
                return ContentGenerator()
    
    def test_enhance_prompt(self, content_generator):
        """Test prompt enhancement functionality."""
        base_prompt = "Generate a space fact"
        content_type = "cosmic_fact"
        
        enhanced = content_generator._enhance_prompt(base_prompt, content_type)
        
        assert "JSON" in enhanced
        assert "HTML" in enhanced
        assert base_prompt in enhanced
        assert content_type in enhanced
    
    def test_clean_json_response(self, content_generator):
        """Test JSON response cleaning."""
        # Test with markdown code blocks
        response_with_markdown = '```json\n{"title": "Test", "content": "Content"}\n```'
        cleaned = content_generator._clean_json_response(response_with_markdown)
        assert cleaned == '{"title": "Test", "content": "Content"}'
        
        # Test with extra text
        response_with_extra = 'Here is the JSON: {"title": "Test"} End of response'
        cleaned = content_generator._clean_json_response(response_with_extra)
        assert cleaned == '{"title": "Test"}'
    
    def test_parse_response_valid_json(self, content_generator):
        """Test parsing valid JSON response."""
        valid_json = '{"title": "Test Title", "content": "Test Content", "hashtags": ["#test"]}'
        
        result = content_generator._parse_response(valid_json, "cosmic_fact")
        
        assert result["title"] == "Test Title"
        assert result["content"] == "Test Content"
        assert result["hashtags"] == ["#test"]
    
    def test_parse_response_invalid_json(self, content_generator):
        """Test parsing invalid JSON response creates fallback."""
        invalid_json = "This is not JSON content"
        
        result = content_generator._parse_response(invalid_json, "cosmic_fact")
        
        assert "title" in result
        assert "content" in result
        assert "hashtags" in result
    
    def test_validate_content_structure(self, content_generator):
        """Test content structure validation."""
        # Valid content
        valid_content = {"title": "Test", "content": "Content"}
        # Should not raise exception
        content_generator._validate_content_structure(valid_content, "cosmic_fact")
        
        # Invalid content (missing fields) should log warnings but not raise
        invalid_content = {"title": ""}
        content_generator._validate_content_structure(invalid_content, "cosmic_fact")
    
    def test_create_fallback_content(self, content_generator):
        """Test fallback content creation."""
        response = "Some response text"
        content_type = "cosmic_fact"
        
        fallback = content_generator._create_fallback_content(response, content_type)
        
        assert "title" in fallback
        assert "content" in fallback
        assert "hashtags" in fallback
        assert content_type.replace('_', ' ').title() in fallback["title"]
