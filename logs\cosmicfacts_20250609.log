{"asctime": "2025-06-09 19:58:22,201", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 19:58:22,203", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 19:58:22,204", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\cosmic_fact.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,204", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\myth_buster.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,205", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\explainer.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,206", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\space_history.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,206", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\cosmic_mystery.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,207", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\what_if_scenario.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,208", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\audience_question.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,208", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 19:58:22,208", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 19:58:22,999", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:23,000", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:23,000", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Weekly Content Schedule:", "taskName": null}
{"asctime": "2025-06-09 19:58:23,001", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 19:58:23,002", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for myth_buster", "taskName": null}
{"asctime": "2025-06-09 19:58:23,003", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for explainer", "taskName": null}
{"asctime": "2025-06-09 19:58:23,004", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for space_history", "taskName": null}
{"asctime": "2025-06-09 19:58:23,004", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_mystery", "taskName": null}
{"asctime": "2025-06-09 19:58:23,005", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for what_if_scenario", "taskName": null}
{"asctime": "2025-06-09 19:58:23,006", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for audience_question", "taskName": null}
{"asctime": "2025-06-09 19:58:36,487", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 19:58:36,487", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 19:58:36,488", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 19:58:36,488", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 19:58:37,107", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:37,107", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:37,107", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Testing connections to external services...", "taskName": null}
{"asctime": "2025-06-09 19:58:37,111", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Testing Telegram connection...", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:41,568", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Bot connected: @Spac3PostBot", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:42,213", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Telegram connection test failed: Chat not found", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:42,214", "name": "cosmicfacts.app", "levelname": "ERROR", "message": "Connection tests failed", "taskName": null}
{"asctime": "2025-06-09 19:58:51,631", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 19:58:51,631", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 19:58:51,632", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 19:58:51,632", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 19:58:52,315", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:52,315", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:52,316", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Starting custom content generation for: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 19:58:52,316", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 19:58:52,317", "name": "src.core.content_generator", "levelname": "INFO", "message": "Generating cosmic_fact content...", "taskName": null}
{"asctime": "2025-06-09 19:58:56,924", "name": "src.core.content_generator", "levelname": "INFO", "message": "Successfully generated cosmic_fact content", "taskName": null}
{"asctime": "2025-06-09 19:58:56,927", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Preparing content for Telegram posting...", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:56,927", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 769 characters", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:57,880", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Telegram API error on attempt 1: Can't parse entities: unsupported start tag \"html\" at byte offset 42", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:06,314", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Telegram API error on attempt 2: Can't parse entities: unsupported start tag \"html\" at byte offset 42", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:12,211", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Telegram API error on attempt 3: Can't parse entities: unsupported start tag \"html\" at byte offset 42", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:12,212", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "All posting attempts failed", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:12,212", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Failed to post content to Telegram", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:12,213", "name": "cosmicfacts.app", "levelname": "ERROR", "message": "Failed to post custom cosmic_fact content", "taskName": null}
{"asctime": "2025-06-09 20:00:06,887", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 20:00:06,889", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 20:00:06,890", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 20:00:06,890", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:07,577", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 20:00:07,578", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 20:00:07,578", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Starting custom content generation for: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:07,579", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:07,580", "name": "src.core.content_generator", "levelname": "INFO", "message": "Generating cosmic_fact content...", "taskName": null}
{"asctime": "2025-06-09 20:00:10,835", "name": "src.core.content_generator", "levelname": "INFO", "message": "Successfully generated cosmic_fact content", "taskName": null}
{"asctime": "2025-06-09 20:00:10,838", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Preparing content for Telegram posting...", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:00:10,839", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 625 characters", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:00:13,023", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Message posted successfully. Message ID: 2", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:00:13,023", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Content posted to Telegram successfully", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:00:13,024", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Custom cosmic_fact content posted successfully!", "taskName": null}
{"asctime": "2025-06-09 20:00:29,747", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,753", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,757", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,763", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,763", "name": "src.core.content_generator", "levelname": "ERROR", "message": "Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)", "taskName": null}
{"asctime": "2025-06-09 20:00:29,764", "name": "src.core.content_generator", "levelname": "INFO", "message": "Creating fallback content structure", "taskName": null}
{"asctime": "2025-06-09 20:00:29,770", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,771", "name": "src.core.content_generator", "levelname": "WARNING", "message": "Missing or empty required field: title", "taskName": null}
{"asctime": "2025-06-09 20:00:29,772", "name": "src.core.content_generator", "levelname": "WARNING", "message": "Missing or empty required field: content", "taskName": null}
{"asctime": "2025-06-09 20:00:29,778", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,779", "name": "src.core.content_generator", "levelname": "INFO", "message": "Creating fallback content structure", "taskName": null}
{"asctime": "2025-06-09 20:00:29,784", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 67 characters", "taskName": null}
{"asctime": "2025-06-09 20:00:29,792", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 31 characters", "taskName": null}
{"asctime": "2025-06-09 20:00:29,803", "name": "src.utils.formatters", "levelname": "WARNING", "message": "Failed to parse JSON content: Expecting value: line 1 column 1 (char 0)", "taskName": null}
{"asctime": "2025-06-09 20:00:29,813", "name": "src.utils.formatters", "levelname": "WARNING", "message": "Message length (5000) exceeds Telegram limit. Truncating...", "taskName": null}
{"asctime": "2025-06-09 20:00:29,821", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 20 characters", "taskName": null}
{"asctime": "2025-06-09 20:00:57,797", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 20:00:57,799", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 20:00:57,800", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 20:00:57,801", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:58,608", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 20:00:58,609", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 20:00:58,609", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Starting daily content generation and posting...", "taskName": null}
{"asctime": "2025-06-09 20:00:58,610", "name": "src.core.scheduler", "levelname": "INFO", "message": "Today's content type: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:58,610", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:58,611", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Generating content for: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:58,611", "name": "src.core.content_generator", "levelname": "INFO", "message": "Generating cosmic_fact content...", "taskName": null}
{"asctime": "2025-06-09 20:01:02,600", "name": "src.core.content_generator", "levelname": "INFO", "message": "Successfully generated cosmic_fact content", "taskName": null}
{"asctime": "2025-06-09 20:01:02,602", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Preparing content for Telegram posting...", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:01:02,603", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 795 characters", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:01:03,802", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Message posted successfully. Message ID: 3", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:01:03,803", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Content posted to Telegram successfully", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:01:03,803", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Daily content posted successfully!", "taskName": null}
{"asctime": "2025-06-09 20:19:26,070", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\cosmic_fact.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,071", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\myth_buster.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,073", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\explainer.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,076", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\space_history.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,077", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\cosmic_mystery.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,078", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\what_if_scenario.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,079", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\audience_question.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,080", "name": "data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 20:19:35,772", "name": "data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 20:26:28,955", "name": "data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 20:26:28,956", "name": "data.topic_manager", "levelname": "INFO", "message": "Added 106 new topics to cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 21:34:20,652", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 21:34:20,653", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 21:34:20,654", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 21:34:20,655", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 21:34:20,655", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Enhanced content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 21:34:21,221", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 21:34:21,222", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 21:34:43,169", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 21:34:43,170", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 21:34:43,170", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 21:34:43,171", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 21:34:43,171", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Enhanced content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 21:34:43,783", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 21:34:43,783", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 21:34:43,784", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Weekly Content Schedule:", "taskName": null}
{"asctime": "2025-06-09 21:34:58,212", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 21:34:58,212", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 21:34:58,213", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 21:34:58,213", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 21:34:58,214", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Enhanced content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 21:34:58,794", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 21:34:58,794", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 21:34:58,795", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Starting custom content generation for: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 21:34:58,795", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 1/3", "taskName": null}
{"asctime": "2025-06-09 21:34:58,795", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 21:34:58,796", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for cosmic_fact: Milky Way contains 100-400 billion stars", "taskName": null}
{"asctime": "2025-06-09 21:34:58,796", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: Milky Way contains 100-400 billion stars", "taskName": null}
{"asctime": "2025-06-09 21:34:58,796", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:35:02,783", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:02,784", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:35:04,525", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:04,526", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:04,526", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality validation passed", "taskName": null}
{"asctime": "2025-06-09 21:35:04,526", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "High-quality content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:04,529", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Preparing content for Telegram posting...", "taskName": "Task-1"}
{"asctime": "2025-06-09 21:35:04,531", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 438 characters", "taskName": "Task-1"}
{"asctime": "2025-06-09 21:35:06,048", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Message posted successfully. Message ID: 4", "taskName": "Task-1"}
{"asctime": "2025-06-09 21:35:06,048", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Content posted to Telegram successfully", "taskName": "Task-1"}
{"asctime": "2025-06-09 21:35:06,048", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Custom cosmic_fact content posted successfully!", "taskName": null}
{"asctime": "2025-06-09 21:35:32,157", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 21:35:32,158", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 21:35:32,158", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 21:35:32,159", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 21:35:32,160", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Enhanced content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 21:35:32,788", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:32,788", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:32,789", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Starting bulk generation: 3 pieces of myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:35:32,789", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Processing bulk request 1/3", "taskName": null}
{"asctime": "2025-06-09 21:35:32,789", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 1/3", "taskName": null}
{"asctime": "2025-06-09 21:35:32,789", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:35:32,790", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: New inflation is obsolete", "taskName": null}
{"asctime": "2025-06-09 21:35:32,791", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: New inflation is obsolete", "taskName": null}
{"asctime": "2025-06-09 21:35:32,791", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:35:36,754", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:36,754", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:35:40,289", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:40,290", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:40,291", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1007) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:35:40,291", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 1", "taskName": null}
{"asctime": "2025-06-09 21:35:40,291", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Trying with a different topic...", "taskName": null}
{"asctime": "2025-06-09 21:35:40,292", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 2/3", "taskName": null}
{"asctime": "2025-06-09 21:35:40,292", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:35:40,293", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: All stars eventually become black holes", "taskName": null}
{"asctime": "2025-06-09 21:35:40,294", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: All stars eventually become black holes", "taskName": null}
{"asctime": "2025-06-09 21:35:40,294", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:35:42,098", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:42,099", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:35:45,458", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:45,459", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:45,459", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1127) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:35:45,460", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 2", "taskName": null}
{"asctime": "2025-06-09 21:35:45,460", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Trying with a different topic...", "taskName": null}
{"asctime": "2025-06-09 21:35:45,460", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 3/3", "taskName": null}
{"asctime": "2025-06-09 21:35:45,460", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:35:45,461", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: Science and pseudoscience clearly separated", "taskName": null}
{"asctime": "2025-06-09 21:35:45,461", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: Science and pseudoscience clearly separated", "taskName": null}
{"asctime": "2025-06-09 21:35:45,462", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:35:47,618", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:47,619", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:35:52,196", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:52,197", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:52,197", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1340) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:35:52,197", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 3", "taskName": null}
{"asctime": "2025-06-09 21:35:52,199", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "All content generation attempts failed quality validation", "taskName": null}
{"asctime": "2025-06-09 21:35:52,199", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Creating fallback content structure for myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:35:54,200", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Processing bulk request 2/3", "taskName": null}
{"asctime": "2025-06-09 21:35:54,201", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 1/3", "taskName": null}
{"asctime": "2025-06-09 21:35:54,201", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:35:54,203", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: Interplanetary dust doesn't exist", "taskName": null}
{"asctime": "2025-06-09 21:35:54,203", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: Interplanetary dust doesn't exist", "taskName": null}
{"asctime": "2025-06-09 21:35:54,203", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:35:56,693", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:35:56,693", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:36:00,511", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:00,511", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:00,511", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1483) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:36:00,512", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 1", "taskName": null}
{"asctime": "2025-06-09 21:36:00,513", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Trying with a different topic...", "taskName": null}
{"asctime": "2025-06-09 21:36:00,513", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 2/3", "taskName": null}
{"asctime": "2025-06-09 21:36:00,513", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:36:00,514", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: Delayed choice experiments prove retrocausality", "taskName": null}
{"asctime": "2025-06-09 21:36:00,515", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: Delayed choice experiments prove retrocausality", "taskName": null}
{"asctime": "2025-06-09 21:36:00,515", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:36:02,839", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:02,840", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:36:06,825", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:06,826", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:06,826", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1443) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:36:06,827", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 2", "taskName": null}
{"asctime": "2025-06-09 21:36:06,827", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Trying with a different topic...", "taskName": null}
{"asctime": "2025-06-09 21:36:06,827", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 3/3", "taskName": null}
{"asctime": "2025-06-09 21:36:06,828", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:36:06,829", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: Determinism is absolutely true", "taskName": null}
{"asctime": "2025-06-09 21:36:06,829", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: Determinism is absolutely true", "taskName": null}
{"asctime": "2025-06-09 21:36:06,829", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:36:09,162", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:09,162", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:36:13,350", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:13,351", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:13,351", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1434) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:36:13,351", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 3", "taskName": null}
{"asctime": "2025-06-09 21:36:13,351", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "All content generation attempts failed quality validation", "taskName": null}
{"asctime": "2025-06-09 21:36:13,351", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Creating fallback content structure for myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:36:15,353", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Processing bulk request 3/3", "taskName": null}
{"asctime": "2025-06-09 21:36:15,353", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 1/3", "taskName": null}
{"asctime": "2025-06-09 21:36:15,353", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:36:15,355", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: Gravitational wave astronomy is complete", "taskName": null}
{"asctime": "2025-06-09 21:36:15,355", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: Gravitational wave astronomy is complete", "taskName": null}
{"asctime": "2025-06-09 21:36:15,355", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:36:17,517", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:17,518", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:36:20,420", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:20,420", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:20,420", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1042) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:36:20,421", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 1", "taskName": null}
{"asctime": "2025-06-09 21:36:20,421", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Trying with a different topic...", "taskName": null}
{"asctime": "2025-06-09 21:36:20,421", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 2/3", "taskName": null}
{"asctime": "2025-06-09 21:36:20,421", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:36:20,422", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: Free will is completely illusory", "taskName": null}
{"asctime": "2025-06-09 21:36:20,423", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: Free will is completely illusory", "taskName": null}
{"asctime": "2025-06-09 21:36:20,424", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:36:22,347", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:22,349", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:36:25,646", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:25,647", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:25,648", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1187) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:36:25,648", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 2", "taskName": null}
{"asctime": "2025-06-09 21:36:25,649", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Trying with a different topic...", "taskName": null}
{"asctime": "2025-06-09 21:36:25,649", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content generation attempt 3/3", "taskName": null}
{"asctime": "2025-06-09 21:36:25,651", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Starting two-stage content generation for: myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:36:25,652", "name": "src.data.topic_manager", "levelname": "INFO", "message": "Selected topic for myth_buster: Cosmic rays are dangerous radiation", "taskName": null}
{"asctime": "2025-06-09 21:36:25,653", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Selected topic from dataset: Cosmic rays are dangerous radiation", "taskName": null}
{"asctime": "2025-06-09 21:36:25,653", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1: Generating raw content...", "taskName": null}
{"asctime": "2025-06-09 21:36:27,789", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 1 content generated successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:27,789", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Stage 2: Humanizing and enhancing content...", "taskName": null}
{"asctime": "2025-06-09 21:36:30,832", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Two-stage content generation completed successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:30,833", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Content quality enhanced successfully", "taskName": null}
{"asctime": "2025-06-09 21:36:30,834", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content length (1004) outside acceptable range", "taskName": null}
{"asctime": "2025-06-09 21:36:30,834", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "Content quality validation failed on attempt 3", "taskName": null}
{"asctime": "2025-06-09 21:36:30,834", "name": "src.core.enhanced_content_generator", "levelname": "WARNING", "message": "All content generation attempts failed quality validation", "taskName": null}
{"asctime": "2025-06-09 21:36:30,834", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Creating fallback content structure for myth_buster", "taskName": null}
{"asctime": "2025-06-09 21:36:30,835", "name": "src.core.enhanced_content_generator", "levelname": "INFO", "message": "Bulk generation completed: 3 results", "taskName": null}
