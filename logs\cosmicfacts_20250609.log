{"asctime": "2025-06-09 19:58:22,201", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 19:58:22,203", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 19:58:22,204", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\cosmic_fact.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,204", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\myth_buster.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,205", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\explainer.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,206", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\space_history.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,206", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\cosmic_mystery.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,207", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\what_if_scenario.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,208", "name": "src.core.scheduler", "levelname": "INFO", "message": "Created default prompt file: src\\data\\prompts\\audience_question.json", "taskName": null}
{"asctime": "2025-06-09 19:58:22,208", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 19:58:22,208", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 19:58:22,999", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:23,000", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:23,000", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Weekly Content Schedule:", "taskName": null}
{"asctime": "2025-06-09 19:58:23,001", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 19:58:23,002", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for myth_buster", "taskName": null}
{"asctime": "2025-06-09 19:58:23,003", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for explainer", "taskName": null}
{"asctime": "2025-06-09 19:58:23,004", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for space_history", "taskName": null}
{"asctime": "2025-06-09 19:58:23,004", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_mystery", "taskName": null}
{"asctime": "2025-06-09 19:58:23,005", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for what_if_scenario", "taskName": null}
{"asctime": "2025-06-09 19:58:23,006", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for audience_question", "taskName": null}
{"asctime": "2025-06-09 19:58:36,487", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 19:58:36,487", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 19:58:36,488", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 19:58:36,488", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 19:58:37,107", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:37,107", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:37,107", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Testing connections to external services...", "taskName": null}
{"asctime": "2025-06-09 19:58:37,111", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Testing Telegram connection...", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:41,568", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Bot connected: @Spac3PostBot", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:42,213", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Telegram connection test failed: Chat not found", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:42,214", "name": "cosmicfacts.app", "levelname": "ERROR", "message": "Connection tests failed", "taskName": null}
{"asctime": "2025-06-09 19:58:51,631", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 19:58:51,631", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 19:58:51,632", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 19:58:51,632", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 19:58:52,315", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:52,315", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 19:58:52,316", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Starting custom content generation for: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 19:58:52,316", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 19:58:52,317", "name": "src.core.content_generator", "levelname": "INFO", "message": "Generating cosmic_fact content...", "taskName": null}
{"asctime": "2025-06-09 19:58:56,924", "name": "src.core.content_generator", "levelname": "INFO", "message": "Successfully generated cosmic_fact content", "taskName": null}
{"asctime": "2025-06-09 19:58:56,927", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Preparing content for Telegram posting...", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:56,927", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 769 characters", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:58:57,880", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Telegram API error on attempt 1: Can't parse entities: unsupported start tag \"html\" at byte offset 42", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:06,314", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Telegram API error on attempt 2: Can't parse entities: unsupported start tag \"html\" at byte offset 42", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:12,211", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Telegram API error on attempt 3: Can't parse entities: unsupported start tag \"html\" at byte offset 42", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:12,212", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "All posting attempts failed", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:12,212", "name": "src.core.telegram_poster", "levelname": "ERROR", "message": "Failed to post content to Telegram", "taskName": "Task-1"}
{"asctime": "2025-06-09 19:59:12,213", "name": "cosmicfacts.app", "levelname": "ERROR", "message": "Failed to post custom cosmic_fact content", "taskName": null}
{"asctime": "2025-06-09 20:00:06,887", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 20:00:06,889", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 20:00:06,890", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 20:00:06,890", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:07,577", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 20:00:07,578", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 20:00:07,578", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Starting custom content generation for: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:07,579", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:07,580", "name": "src.core.content_generator", "levelname": "INFO", "message": "Generating cosmic_fact content...", "taskName": null}
{"asctime": "2025-06-09 20:00:10,835", "name": "src.core.content_generator", "levelname": "INFO", "message": "Successfully generated cosmic_fact content", "taskName": null}
{"asctime": "2025-06-09 20:00:10,838", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Preparing content for Telegram posting...", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:00:10,839", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 625 characters", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:00:13,023", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Message posted successfully. Message ID: 2", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:00:13,023", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Content posted to Telegram successfully", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:00:13,024", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Custom cosmic_fact content posted successfully!", "taskName": null}
{"asctime": "2025-06-09 20:00:29,747", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,753", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,757", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,763", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,763", "name": "src.core.content_generator", "levelname": "ERROR", "message": "Failed to parse JSON response: Expecting value: line 1 column 1 (char 0)", "taskName": null}
{"asctime": "2025-06-09 20:00:29,764", "name": "src.core.content_generator", "levelname": "INFO", "message": "Creating fallback content structure", "taskName": null}
{"asctime": "2025-06-09 20:00:29,770", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,771", "name": "src.core.content_generator", "levelname": "WARNING", "message": "Missing or empty required field: title", "taskName": null}
{"asctime": "2025-06-09 20:00:29,772", "name": "src.core.content_generator", "levelname": "WARNING", "message": "Missing or empty required field: content", "taskName": null}
{"asctime": "2025-06-09 20:00:29,778", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:29,779", "name": "src.core.content_generator", "levelname": "INFO", "message": "Creating fallback content structure", "taskName": null}
{"asctime": "2025-06-09 20:00:29,784", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 67 characters", "taskName": null}
{"asctime": "2025-06-09 20:00:29,792", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 31 characters", "taskName": null}
{"asctime": "2025-06-09 20:00:29,803", "name": "src.utils.formatters", "levelname": "WARNING", "message": "Failed to parse JSON content: Expecting value: line 1 column 1 (char 0)", "taskName": null}
{"asctime": "2025-06-09 20:00:29,813", "name": "src.utils.formatters", "levelname": "WARNING", "message": "Message length (5000) exceeds Telegram limit. Truncating...", "taskName": null}
{"asctime": "2025-06-09 20:00:29,821", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 20 characters", "taskName": null}
{"asctime": "2025-06-09 20:00:57,797", "name": "cosmicfacts.app", "levelname": "INFO", "message": "CosmicFacts application starting...", "taskName": null}
{"asctime": "2025-06-09 20:00:57,799", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Initializing application components...", "taskName": null}
{"asctime": "2025-06-09 20:00:57,800", "name": "src.core.scheduler", "levelname": "INFO", "message": "Content scheduler initialized", "taskName": null}
{"asctime": "2025-06-09 20:00:57,801", "name": "src.core.content_generator", "levelname": "INFO", "message": "Content generator initialized with model: gemini-2.0-flash", "taskName": null}
{"asctime": "2025-06-09 20:00:58,608", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Telegram poster initialized successfully", "taskName": null}
{"asctime": "2025-06-09 20:00:58,609", "name": "cosmicfacts.app", "levelname": "INFO", "message": "All components initialized successfully", "taskName": null}
{"asctime": "2025-06-09 20:00:58,609", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Starting daily content generation and posting...", "taskName": null}
{"asctime": "2025-06-09 20:00:58,610", "name": "src.core.scheduler", "levelname": "INFO", "message": "Today's content type: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:58,610", "name": "src.core.scheduler", "levelname": "INFO", "message": "Loaded prompt for cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:58,611", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Generating content for: cosmic_fact", "taskName": null}
{"asctime": "2025-06-09 20:00:58,611", "name": "src.core.content_generator", "levelname": "INFO", "message": "Generating cosmic_fact content...", "taskName": null}
{"asctime": "2025-06-09 20:01:02,600", "name": "src.core.content_generator", "levelname": "INFO", "message": "Successfully generated cosmic_fact content", "taskName": null}
{"asctime": "2025-06-09 20:01:02,602", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Preparing content for Telegram posting...", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:01:02,603", "name": "src.utils.formatters", "levelname": "INFO", "message": "Content formatted successfully. Length: 795 characters", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:01:03,802", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Message posted successfully. Message ID: 3", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:01:03,803", "name": "src.core.telegram_poster", "levelname": "INFO", "message": "Content posted to Telegram successfully", "taskName": "Task-1"}
{"asctime": "2025-06-09 20:01:03,803", "name": "cosmicfacts.app", "levelname": "INFO", "message": "Daily content posted successfully!", "taskName": null}
{"asctime": "2025-06-09 20:19:26,070", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\cosmic_fact.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,071", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\myth_buster.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,073", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\explainer.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,076", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\space_history.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,077", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\cosmic_mystery.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,078", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\what_if_scenario.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,079", "name": "data.topic_manager", "levelname": "INFO", "message": "Created topic dataset: src\\data\\topics\\audience_question.csv", "taskName": null}
{"asctime": "2025-06-09 20:19:26,080", "name": "data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 20:19:35,772", "name": "data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 20:26:28,955", "name": "data.topic_manager", "levelname": "INFO", "message": "Topic manager initialized", "taskName": null}
{"asctime": "2025-06-09 20:26:28,956", "name": "data.topic_manager", "levelname": "INFO", "message": "Added 106 new topics to cosmic_fact", "taskName": null}
