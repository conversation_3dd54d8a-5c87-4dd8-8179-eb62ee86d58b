"""
Poll poster for sending polls to Telegram users.
Handles poll creation and sending via Telegram Bot API.
"""

import json
import requests
from typing import Dict, Any, Optional, List
from src.utils.config import config
from src.utils.logger import get_logger
from src.core.poll_generator import PollGenerator

logger = get_logger(__name__)


class PollPoster:
    """Handle posting polls to Telegram."""
    
    def __init__(self):
        """Initialize the poll poster."""
        self.bot_token = config.telegram_bot_token
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        self.poll_generator = PollGenerator()
        
        if not self.bot_token:
            logger.error("Telegram bot token not configured")
            raise ValueError("Telegram bot token is required")
    
    def send_poll_to_user(self, user_id: int, custom_topic: str = None) -> Dict[str, Any]:
        """
        Send a poll to a specific user.
        
        Args:
            user_id: Telegram user ID to send poll to
            custom_topic: Optional custom topic for the poll
            
        Returns:
            Dictionary with poll data and send result
        """
        try:
            logger.info(f"Generating poll for user {user_id}")
            
            # Generate poll
            poll_data = self.poll_generator.generate_poll(custom_topic)
            
            # Send poll via Telegram API
            send_result = self._send_poll_via_api(user_id, poll_data)
            
            return {
                "poll_data": poll_data,
                "send_result": send_result,
                "success": send_result.get("ok", False),
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error sending poll to user {user_id}: {str(e)}")
            return {
                "error": str(e),
                "success": False,
                "user_id": user_id
            }
    
    def _send_poll_via_api(self, chat_id: int, poll_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send poll using Telegram Bot API sendPoll method.
        
        Args:
            chat_id: Chat ID to send poll to
            poll_data: Poll data with question and options
            
        Returns:
            API response
        """
        try:
            url = f"{self.base_url}/sendPoll"
            
            # Prepare poll parameters
            params = {
                "chat_id": chat_id,
                "question": poll_data["question"],
                "options": json.dumps(poll_data["options"]),
                "is_anonymous": poll_data.get("is_anonymous", True),
                "type": poll_data.get("type", "regular"),
                "allows_multiple_answers": poll_data.get("allows_multiple_answers", False)
            }
            
            logger.info(f"Sending poll to chat {chat_id}")
            logger.debug(f"Poll params: {params}")
            
            # Send request
            response = requests.post(url, data=params, timeout=30)
            result = response.json()
            
            if result.get("ok"):
                logger.info(f"Poll sent successfully to chat {chat_id}")
                logger.info(f"Message ID: {result.get('result', {}).get('message_id')}")
            else:
                logger.error(f"Failed to send poll: {result.get('description', 'Unknown error')}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Network error sending poll: {str(e)}")
            return {"ok": False, "error": f"Network error: {str(e)}"}
        except Exception as e:
            logger.error(f"Error sending poll via API: {str(e)}")
            return {"ok": False, "error": str(e)}
    
    def send_poll_preview_to_user(self, user_id: int, custom_topic: str = None) -> Dict[str, Any]:
        """
        Send a text preview of the poll instead of an actual poll.
        Useful for testing or when polls can't be sent directly.
        
        Args:
            user_id: Telegram user ID to send preview to
            custom_topic: Optional custom topic for the poll
            
        Returns:
            Dictionary with poll data and send result
        """
        try:
            logger.info(f"Generating poll preview for user {user_id}")
            
            # Generate poll
            poll_data = self.poll_generator.generate_poll(custom_topic)
            
            # Format as text message
            preview_text = self._format_poll_as_text(poll_data)
            
            # Send as regular message
            send_result = self._send_text_message(user_id, preview_text)
            
            return {
                "poll_data": poll_data,
                "preview_text": preview_text,
                "send_result": send_result,
                "success": send_result.get("ok", False),
                "user_id": user_id,
                "preview_mode": True
            }
            
        except Exception as e:
            logger.error(f"Error sending poll preview to user {user_id}: {str(e)}")
            return {
                "error": str(e),
                "success": False,
                "user_id": user_id,
                "preview_mode": True
            }
    
    def _format_poll_as_text(self, poll_data: Dict[str, Any]) -> str:
        """Format poll data as a text message."""
        
        text_parts = [
            "🗳️ <b>Sunday Space Poll!</b>",
            "",
            f"<b>Question:</b> {poll_data['question']}",
            "",
            "<b>Options:</b>"
        ]
        
        # Add options with emojis
        option_emojis = ["🔴", "🔵", "🟢", "🟡", "🟠", "🟣", "⚫", "⚪", "🟤", "🔶"]
        
        for i, option in enumerate(poll_data['options']):
            emoji = option_emojis[i] if i < len(option_emojis) else f"{i+1}."
            text_parts.append(f"{emoji} {option}")
        
        text_parts.extend([
            "",
            "💭 <i>What's your choice? Let me know in the comments!</i>",
            "",
            f"#SpacePoll #SundayEngagement #{poll_data.get('metadata', {}).get('topic', 'space').replace(' ', '')}"
        ])
        
        return "\n".join(text_parts)
    
    def _send_text_message(self, chat_id: int, text: str) -> Dict[str, Any]:
        """Send a text message via Telegram API."""
        try:
            url = f"{self.base_url}/sendMessage"
            
            params = {
                "chat_id": chat_id,
                "text": text,
                "parse_mode": "HTML"
            }
            
            response = requests.post(url, data=params, timeout=30)
            result = response.json()
            
            if result.get("ok"):
                logger.info(f"Text message sent successfully to chat {chat_id}")
            else:
                logger.error(f"Failed to send text message: {result.get('description', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error sending text message: {str(e)}")
            return {"ok": False, "error": str(e)}
    
    def test_poll_generation(self, count: int = 3) -> List[Dict[str, Any]]:
        """Generate test polls without sending them."""
        test_polls = []
        
        for i in range(count):
            try:
                poll_data = self.poll_generator.generate_poll()
                preview_text = self._format_poll_as_text(poll_data)
                
                test_polls.append({
                    "poll_number": i + 1,
                    "poll_data": poll_data,
                    "preview_text": preview_text
                })
                
            except Exception as e:
                logger.error(f"Error generating test poll {i+1}: {str(e)}")
                test_polls.append({
                    "poll_number": i + 1,
                    "error": str(e)
                })
        
        return test_polls
    
    def get_poll_statistics(self) -> Dict[str, Any]:
        """Get poll generation statistics."""
        return self.poll_generator.get_poll_statistics()


def test_poll_system():
    """Test the poll system."""
    try:
        poster = PollPoster()
        
        print("=== POLL SYSTEM TEST ===")
        
        # Test poll generation
        print("\n1. Testing poll generation...")
        test_polls = poster.test_poll_generation(3)
        
        for poll in test_polls:
            if "error" in poll:
                print(f"Poll {poll['poll_number']}: ERROR - {poll['error']}")
            else:
                print(f"\nPoll {poll['poll_number']}:")
                print(f"Question: {poll['poll_data']['question']}")
                print("Options:")
                for i, option in enumerate(poll['poll_data']['options'], 1):
                    print(f"  {i}. {option}")
        
        # Test statistics
        print("\n2. Poll statistics:")
        stats = poster.get_poll_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("\n=== TEST COMPLETE ===")
        
    except Exception as e:
        print(f"Test failed: {str(e)}")


if __name__ == "__main__":
    test_poll_system()
