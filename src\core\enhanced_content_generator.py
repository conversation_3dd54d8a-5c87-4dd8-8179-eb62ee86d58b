"""
Enhanced two-stage content generation system for CosmicFacts.
Implements sophisticated prompt engineering and content humanization.
"""

import json
import time
from typing import Dict, Any, Optional, Tuple
import google.generativeai as genai
from src.utils.config import config
from src.utils.logger import get_logger
from src.data.prompts_engine import PromptEngineFactory
from src.data.topic_manager import TopicManager

logger = get_logger(__name__)


class EnhancedContentGenerator:
    """Enhanced content generator with two-stage processing and advanced prompt engineering."""
    
    def __init__(self):
        """Initialize the enhanced content generator."""
        try:
            genai.configure(api_key=config.gemini_api_key)
            self.model = genai.GenerativeModel(config.gemini_model)
            self.topic_manager = TopicManager()
            logger.info(f"Enhanced content generator initialized with model: {config.gemini_model}")
        except Exception as e:
            logger.error(f"Failed to initialize enhanced content generator: {str(e)}")
            raise
    
    def generate_content(self, content_type: str, custom_topic: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate content using two-stage process with advanced prompt engineering.
        
        Args:
            content_type: Type of content to generate
            custom_topic: Optional custom topic (if None, will select from dataset)
            context: Optional context for prompt customization
            
        Returns:
            Generated and humanized content as dictionary
        """
        logger.info(f"Starting two-stage content generation for: {content_type}")
        
        try:
            # Get topic
            if custom_topic:
                topic = custom_topic
                logger.info(f"Using custom topic: {topic}")
            else:
                topic = self.topic_manager.get_topic(content_type)
                if not topic:
                    raise ValueError(f"No topics available for content type: {content_type}")
                logger.info(f"Selected topic from dataset: {topic}")
            
            # Get prompt engine
            prompt_engine = PromptEngineFactory.get_engine(content_type)
            
            # Stage 1: Generate raw content
            logger.info("Stage 1: Generating raw content...")
            stage1_prompt = prompt_engine.generate_stage1_prompt(topic, context)
            raw_content = self._generate_with_retry(stage1_prompt, stage="Stage 1")
            
            # Parse Stage 1 response
            stage1_data = self._parse_response(raw_content, f"{content_type}_stage1")
            logger.info("Stage 1 content generated successfully")
            
            # Stage 2: Humanize and enhance content
            logger.info("Stage 2: Humanizing and enhancing content...")
            stage2_prompt = prompt_engine.generate_stage2_prompt(json.dumps(stage1_data), context)
            enhanced_content = self._generate_with_retry(stage2_prompt, stage="Stage 2")
            
            # Parse Stage 2 response
            final_data = self._parse_response(enhanced_content, f"{content_type}_stage2")
            
            # Add metadata
            final_data['metadata'] = {
                'content_type': content_type,
                'topic': topic,
                'generation_stages': 2,
                'timestamp': time.time(),
                'custom_topic': custom_topic is not None
            }
            
            logger.info("Two-stage content generation completed successfully")
            return final_data
            
        except Exception as e:
            logger.error(f"Error in two-stage content generation: {str(e)}")
            raise
    
    def generate_batch_content(self, content_type: str, count: int = 5, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Generate multiple pieces of content for the same content type.
        
        Args:
            content_type: Type of content to generate
            count: Number of content pieces to generate
            context: Optional context for prompt customization
            
        Returns:
            List of generated content dictionaries
        """
        logger.info(f"Starting batch generation of {count} {content_type} pieces")
        
        results = []
        for i in range(count):
            try:
                logger.info(f"Generating batch item {i+1}/{count}")
                content = self.generate_content(content_type, context=context)
                results.append(content)
                
                # Add delay between generations to avoid rate limiting
                if i < count - 1:
                    time.sleep(2)
                    
            except Exception as e:
                logger.error(f"Error generating batch item {i+1}: {str(e)}")
                continue
        
        logger.info(f"Batch generation completed: {len(results)}/{count} successful")
        return results
    
    def get_topic_statistics(self) -> Dict[str, Any]:
        """Get statistics about topic usage across all content types."""
        return self.topic_manager.get_topic_stats()
    
    def add_topics_to_dataset(self, content_type: str, topics: List[str]) -> bool:
        """Add new topics to a content type's dataset."""
        return self.topic_manager.add_topics(content_type, topics)
    
    def reset_topic_usage(self, content_type: str = None) -> bool:
        """Reset topic usage tracking for specified type or all types."""
        return self.topic_manager.reset_used_topics(content_type)
    
    def _generate_with_retry(self, prompt: str, stage: str = "Generation") -> str:
        """
        Generate content with retry logic for API failures.
        
        Args:
            prompt: The prompt for content generation
            stage: Stage identifier for logging
            
        Returns:
            Generated content response
        """
        max_retries = config.max_retries
        retry_delay = config.retry_delay
        
        for attempt in range(max_retries):
            try:
                logger.debug(f"{stage} attempt {attempt + 1}/{max_retries}")
                
                response = self.model.generate_content(prompt)
                
                if response.text:
                    return response.text
                else:
                    raise ValueError(f"Empty response from Gemini API in {stage}")
                    
            except Exception as e:
                logger.warning(f"{stage} attempt {attempt + 1} failed: {str(e)}")
                
                if attempt < max_retries - 1:
                    logger.info(f"Retrying {stage} in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"All {stage} attempts failed")
                    raise
    
    def _parse_response(self, response: str, stage_id: str) -> Dict[str, Any]:
        """
        Parse and validate the AI response.
        
        Args:
            response: Raw response from Gemini API
            stage_id: Stage identifier for logging
            
        Returns:
            Parsed and validated content dictionary
        """
        try:
            # Clean the response to extract JSON
            cleaned_response = self._clean_json_response(response)
            
            # Parse JSON
            content_data = json.loads(cleaned_response)
            
            # Validate required fields
            self._validate_content_structure(content_data, stage_id)
            
            return content_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response in {stage_id}: {str(e)}")
            logger.debug(f"Raw response: {response}")
            
            # Return fallback content structure
            return self._create_fallback_content(response, stage_id)
        
        except Exception as e:
            logger.error(f"Error parsing response in {stage_id}: {str(e)}")
            raise
    
    def _clean_json_response(self, response: str) -> str:
        """
        Clean the response to extract valid JSON.
        
        Args:
            response: Raw response from API
            
        Returns:
            Cleaned JSON string
        """
        # Remove potential markdown code blocks
        response = response.strip()
        
        # Find JSON content between braces
        start_idx = response.find('{')
        end_idx = response.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return response[start_idx:end_idx + 1]
        
        # If no valid JSON structure found, return original
        return response
    
    def _validate_content_structure(self, content_data: Dict[str, Any], stage_id: str) -> None:
        """
        Validate that the content has required structure.
        
        Args:
            content_data: Parsed content dictionary
            stage_id: Stage identifier for validation
        """
        required_fields = ["title", "content"]
        
        for field in required_fields:
            if field not in content_data or not content_data[field]:
                logger.warning(f"Missing or empty required field '{field}' in {stage_id}")
    
    def _create_fallback_content(self, response: str, stage_id: str) -> Dict[str, Any]:
        """
        Create fallback content structure when JSON parsing fails.
        
        Args:
            response: Raw response text
            stage_id: Stage identifier
            
        Returns:
            Fallback content dictionary
        """
        logger.info(f"Creating fallback content structure for {stage_id}")
        
        return {
            "title": f"Cosmic Content ({stage_id})",
            "content": response[:500] + "..." if len(response) > 500 else response,
            "hashtags": ["#space", "#astronomy", "#cosmicfacts"]
        }
