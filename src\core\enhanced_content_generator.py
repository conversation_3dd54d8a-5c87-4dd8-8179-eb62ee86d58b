"""
Enhanced two-stage content generation system for CosmicFacts.
Implements sophisticated prompt engineering and content humanization.
"""

import json
import time
from typing import Dict, Any, Optional, Tuple, List
import google.generativeai as genai
from src.utils.config import config
from src.utils.logger import get_logger
from src.data.prompts_engine import PromptEngineFactory
from src.data.topic_manager import TopicManager

logger = get_logger(__name__)


class EnhancedContentGenerator:
    """Enhanced content generator with two-stage processing and advanced prompt engineering."""
    
    def __init__(self):
        """Initialize the enhanced content generator."""
        try:
            genai.configure(api_key=config.gemini_api_key)
            self.model = genai.GenerativeModel(config.gemini_model)
            self.topic_manager = TopicManager()
            logger.info(f"Enhanced content generator initialized with model: {config.gemini_model}")
        except Exception as e:
            logger.error(f"Failed to initialize enhanced content generator: {str(e)}")
            raise
    
    def generate_content(self, content_type: str, custom_topic: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate content using two-stage process with advanced prompt engineering.
        
        Args:
            content_type: Type of content to generate
            custom_topic: Optional custom topic (if None, will select from dataset)
            context: Optional context for prompt customization
            
        Returns:
            Generated and humanized content as dictionary
        """
        logger.info(f"Starting two-stage content generation for: {content_type}")
        
        try:
            # Get topic
            if custom_topic:
                topic = custom_topic
                logger.info(f"Using custom topic: {topic}")
            else:
                topic = self.topic_manager.get_topic(content_type)
                if not topic:
                    raise ValueError(f"No topics available for content type: {content_type}")
                logger.info(f"Selected topic from dataset: {topic}")
            
            # Get prompt engine
            prompt_engine = PromptEngineFactory.get_engine(content_type)
            
            # Stage 1: Generate raw content
            logger.info("Stage 1: Generating raw content...")
            stage1_prompt = prompt_engine.generate_stage1_prompt(topic, context)
            raw_content = self._generate_with_retry(stage1_prompt, stage="Stage 1")
            
            # Parse Stage 1 response
            stage1_data = self._parse_response(raw_content, f"{content_type}_stage1")
            logger.info("Stage 1 content generated successfully")
            
            # Stage 2: Humanize and enhance content
            logger.info("Stage 2: Humanizing and enhancing content...")
            stage2_prompt = prompt_engine.generate_stage2_prompt(json.dumps(stage1_data), context)
            enhanced_content = self._generate_with_retry(stage2_prompt, stage="Stage 2")
            
            # Parse Stage 2 response
            final_data = self._parse_response(enhanced_content, f"{content_type}_stage2")
            
            # Add metadata
            final_data['metadata'] = {
                'content_type': content_type,
                'topic': topic,
                'generation_stages': 2,
                'timestamp': time.time(),
                'custom_topic': custom_topic is not None
            }
            
            logger.info("Two-stage content generation completed successfully")
            return final_data
            
        except Exception as e:
            logger.error(f"Error in two-stage content generation: {str(e)}")
            raise
    
    def generate_batch_content(self, content_type: str, count: int = 5, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Generate multiple pieces of content for the same content type.
        
        Args:
            content_type: Type of content to generate
            count: Number of content pieces to generate
            context: Optional context for prompt customization
            
        Returns:
            List of generated content dictionaries
        """
        logger.info(f"Starting batch generation of {count} {content_type} pieces")
        
        results = []
        for i in range(count):
            try:
                logger.info(f"Generating batch item {i+1}/{count}")
                content = self.generate_content(content_type, context=context)
                results.append(content)
                
                # Add delay between generations to avoid rate limiting
                if i < count - 1:
                    time.sleep(2)
                    
            except Exception as e:
                logger.error(f"Error generating batch item {i+1}: {str(e)}")
                continue
        
        logger.info(f"Batch generation completed: {len(results)}/{count} successful")
        return results
    
    def get_topic_statistics(self) -> Dict[str, Any]:
        """Get statistics about topic usage across all content types."""
        return self.topic_manager.get_topic_stats()
    
    def add_topics_to_dataset(self, content_type: str, topics: List[str]) -> bool:
        """Add new topics to a content type's dataset."""
        return self.topic_manager.add_topics(content_type, topics)
    
    def reset_topic_usage(self, content_type: str = None) -> bool:
        """Reset topic usage tracking for specified type or all types."""
        return self.topic_manager.reset_used_topics(content_type)
    
    def _generate_with_retry(self, prompt: str, stage: str = "Generation") -> str:
        """
        Generate content with retry logic for API failures.
        
        Args:
            prompt: The prompt for content generation
            stage: Stage identifier for logging
            
        Returns:
            Generated content response
        """
        max_retries = config.max_retries
        retry_delay = config.retry_delay
        
        for attempt in range(max_retries):
            try:
                logger.debug(f"{stage} attempt {attempt + 1}/{max_retries}")
                
                response = self.model.generate_content(prompt)
                
                if response.text:
                    return response.text
                else:
                    raise ValueError(f"Empty response from Gemini API in {stage}")
                    
            except Exception as e:
                logger.warning(f"{stage} attempt {attempt + 1} failed: {str(e)}")
                
                if attempt < max_retries - 1:
                    logger.info(f"Retrying {stage} in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"All {stage} attempts failed")
                    raise
    
    def _parse_response(self, response: str, stage_id: str) -> Dict[str, Any]:
        """
        Parse and validate the AI response.
        
        Args:
            response: Raw response from Gemini API
            stage_id: Stage identifier for logging
            
        Returns:
            Parsed and validated content dictionary
        """
        try:
            # Clean the response to extract JSON
            cleaned_response = self._clean_json_response(response)
            
            # Parse JSON
            content_data = json.loads(cleaned_response)
            
            # Validate required fields
            self._validate_content_structure(content_data, stage_id)
            
            return content_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response in {stage_id}: {str(e)}")
            logger.debug(f"Raw response: {response}")
            
            # Return fallback content structure
            return self._create_fallback_content(response, stage_id)
        
        except Exception as e:
            logger.error(f"Error parsing response in {stage_id}: {str(e)}")
            raise
    
    def _clean_json_response(self, response: str) -> str:
        """
        Clean the response to extract valid JSON.
        
        Args:
            response: Raw response from API
            
        Returns:
            Cleaned JSON string
        """
        # Remove potential markdown code blocks
        response = response.strip()
        
        # Find JSON content between braces
        start_idx = response.find('{')
        end_idx = response.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return response[start_idx:end_idx + 1]
        
        # If no valid JSON structure found, return original
        return response
    
    def _validate_content_structure(self, content_data: Dict[str, Any], stage_id: str) -> None:
        """
        Validate that the content has required structure.
        
        Args:
            content_data: Parsed content dictionary
            stage_id: Stage identifier for validation
        """
        required_fields = ["title", "content"]
        
        for field in required_fields:
            if field not in content_data or not content_data[field]:
                logger.warning(f"Missing or empty required field '{field}' in {stage_id}")
    
    def _create_fallback_content(self, response: str, stage_id: str) -> Dict[str, Any]:
        """
        Create fallback content structure when JSON parsing fails.
        
        Args:
            response: Raw response text
            stage_id: Stage identifier
            
        Returns:
            Fallback content dictionary
        """
        logger.info(f"Creating fallback content structure for {stage_id}")
        
        return {
            "title": f"Cosmic Content ({stage_id})",
            "content": response[:500] + "..." if len(response) > 500 else response,
            "hashtags": ["#space", "#astronomy", "#cosmicfacts"]
        }

    def validate_content_quality(self, content_data: Dict[str, Any]) -> bool:
        """
        Validate the quality of generated content.

        Args:
            content_data: Generated content to validate

        Returns:
            True if content meets quality standards, False otherwise
        """
        try:
            # Check required fields
            required_fields = ["title", "content", "hashtags"]
            for field in required_fields:
                if field not in content_data or not content_data[field]:
                    logger.warning(f"Missing required field: {field}")
                    return False

            # Check content length
            title_length = len(content_data["title"])
            content_length = len(content_data["content"])

            if title_length < 10 or title_length > 200:
                logger.warning(f"Title length ({title_length}) outside acceptable range")
                return False

            # Optimal Telegram content: 200-600 characters for engagement
            # Maximum Telegram message: 4096 characters
            if content_length < 150 or content_length > 650:
                logger.warning(f"Content length ({content_length}) outside optimal range (150-650 chars)")
                return False

            # Check hashtags
            hashtags = content_data["hashtags"]
            if not isinstance(hashtags, list) or len(hashtags) < 3 or len(hashtags) > 10:
                logger.warning(f"Hashtags count ({len(hashtags) if isinstance(hashtags, list) else 'invalid'}) outside acceptable range")
                return False

            # Check for HTML formatting issues
            content_text = content_data["content"]
            if "<html>" in content_text.lower() or "<body>" in content_text.lower():
                logger.warning("Content contains document structure HTML tags")
                return False

            logger.info("Content quality validation passed")
            return True

        except Exception as e:
            logger.error(f"Error validating content quality: {str(e)}")
            return False

    def enhance_content_quality(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance content quality by applying post-processing improvements.

        Args:
            content_data: Content to enhance

        Returns:
            Enhanced content
        """
        try:
            enhanced_data = content_data.copy()

            # Clean and enhance title
            title = enhanced_data["title"].strip()
            if not title.endswith(("!", "?", ".")):
                title += "!"
            enhanced_data["title"] = title

            # Clean and enhance content
            content = enhanced_data["content"].strip()

            # Remove any document structure HTML
            import re
            content = re.sub(r'</?(?:html|body|head|meta|title|div|p|span)[^>]*>', '', content, flags=re.IGNORECASE)

            # Ensure proper spacing
            content = re.sub(r'\s+', ' ', content)
            content = re.sub(r'\s*\n\s*', '\n', content)

            # Ensure content ends with proper punctuation
            if content and not content.endswith((".", "!", "?")):
                content += "."

            enhanced_data["content"] = content

            # Ensure hashtags are properly formatted
            hashtags = enhanced_data["hashtags"]
            if isinstance(hashtags, list):
                cleaned_hashtags = []
                for tag in hashtags:
                    tag = str(tag).strip()
                    if not tag.startswith("#"):
                        tag = "#" + tag
                    # Remove spaces and special characters
                    tag = re.sub(r'[^\w#]', '', tag)
                    if len(tag) > 1:  # Must have content after #
                        cleaned_hashtags.append(tag.lower())

                # Remove duplicates while preserving order
                seen = set()
                unique_hashtags = []
                for tag in cleaned_hashtags:
                    if tag not in seen:
                        seen.add(tag)
                        unique_hashtags.append(tag)

                enhanced_data["hashtags"] = unique_hashtags[:8]  # Limit to 8 hashtags

            logger.info("Content quality enhanced successfully")
            return enhanced_data

        except Exception as e:
            logger.error(f"Error enhancing content quality: {str(e)}")
            return content_data

    def generate_content_with_validation(self, content_type: str, custom_topic: str = None,
                                       context: Dict[str, Any] = None, max_retries: int = 3) -> Dict[str, Any]:
        """
        Generate content with quality validation and retry logic.

        Args:
            content_type: Type of content to generate
            custom_topic: Optional custom topic
            context: Optional context for generation
            max_retries: Maximum number of generation attempts

        Returns:
            High-quality generated content
        """
        for attempt in range(max_retries):
            try:
                logger.info(f"Content generation attempt {attempt + 1}/{max_retries}")

                # Generate content
                content_data = self.generate_content(content_type, custom_topic, context)

                # Enhance quality
                enhanced_content = self.enhance_content_quality(content_data)

                # Validate quality
                if self.validate_content_quality(enhanced_content):
                    logger.info("High-quality content generated successfully")
                    return enhanced_content
                else:
                    logger.warning(f"Content quality validation failed on attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        # Try with a different topic if using dataset
                        if not custom_topic:
                            logger.info("Trying with a different topic...")
                            continue

            except Exception as e:
                logger.error(f"Content generation attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    raise

        # If all attempts failed, return the last attempt with basic fallback
        logger.warning("All content generation attempts failed quality validation")
        return self._create_fallback_content("Quality validation failed", content_type)

    def get_content_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics about content generation and topics.

        Returns:
            Statistics dictionary
        """
        try:
            topic_stats = self.get_topic_statistics()

            # Calculate total topics and usage
            total_topics = sum(stats["total"] for stats in topic_stats.values())
            total_used = sum(stats["used"] for stats in topic_stats.values())
            total_remaining = sum(stats["remaining"] for stats in topic_stats.values())

            # Calculate usage percentages
            usage_percentages = {}
            for content_type, stats in topic_stats.items():
                if stats["total"] > 0:
                    usage_percentages[content_type] = (stats["used"] / stats["total"]) * 100
                else:
                    usage_percentages[content_type] = 0

            overall_usage = (total_used / total_topics * 100) if total_topics > 0 else 0

            return {
                "topic_statistics": topic_stats,
                "total_topics": total_topics,
                "total_used": total_used,
                "total_remaining": total_remaining,
                "overall_usage_percentage": round(overall_usage, 2),
                "usage_percentages": {k: round(v, 2) for k, v in usage_percentages.items()},
                "content_types": list(topic_stats.keys()),
                "least_used_type": min(usage_percentages.items(), key=lambda x: x[1])[0] if usage_percentages else None,
                "most_used_type": max(usage_percentages.items(), key=lambda x: x[1])[0] if usage_percentages else None
            }

        except Exception as e:
            logger.error(f"Error getting content statistics: {str(e)}")
            return {"error": str(e)}

    def bulk_generate_content(self, content_requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate multiple pieces of content in bulk.

        Args:
            content_requests: List of content generation requests
                Each request should have: {"content_type": str, "custom_topic": str (optional), "context": dict (optional)}

        Returns:
            List of generated content pieces
        """
        results = []

        for i, request in enumerate(content_requests):
            try:
                logger.info(f"Processing bulk request {i+1}/{len(content_requests)}")

                content_type = request.get("content_type")
                custom_topic = request.get("custom_topic")
                context = request.get("context", {})

                if not content_type:
                    logger.error(f"Missing content_type in request {i+1}")
                    continue

                # Add request metadata to context
                context["bulk_request"] = True
                context["request_index"] = i
                context["total_requests"] = len(content_requests)

                # Generate content
                content = self.generate_content_with_validation(
                    content_type=content_type,
                    custom_topic=custom_topic,
                    context=context
                )

                # Add request metadata to result
                content["request_metadata"] = {
                    "request_index": i,
                    "content_type": content_type,
                    "custom_topic": custom_topic,
                    "bulk_generation": True
                }

                results.append(content)

                # Add delay between requests to avoid rate limiting
                if i < len(content_requests) - 1:
                    import time
                    time.sleep(2)

            except Exception as e:
                logger.error(f"Error processing bulk request {i+1}: {str(e)}")
                # Add error result
                results.append({
                    "error": str(e),
                    "request_metadata": {
                        "request_index": i,
                        "content_type": request.get("content_type", "unknown"),
                        "bulk_generation": True,
                        "failed": True
                    }
                })

        logger.info(f"Bulk generation completed: {len(results)} results")
        return results
