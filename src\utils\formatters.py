"""
Content formatting utilities for Telegram posts.
"""

import re
import json
from typing import Dict, Any, Optional
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TelegramFormatter:
    """Formatter class for preparing content for Telegram posts."""
    
    # Telegram message limits
    MAX_MESSAGE_LENGTH = 4096
    MAX_CAPTION_LENGTH = 1024
    
    def __init__(self):
        """Initialize the Telegram formatter."""
        pass
    
    def format_content(self, content_data: Dict[str, Any]) -> str:
        """
        Format generated content for Telegram posting.
        
        Args:
            content_data: Dictionary containing the generated content
            
        Returns:
            Formatted content string ready for Telegram
        """
        try:
            # Extract content from the data
            if isinstance(content_data, str):
                # If content is a string, try to parse as JSON
                content_data = self._parse_json_content(content_data)
            
            # Build the formatted message
            formatted_message = self._build_message(content_data)
            
            # Ensure message length is within Telegram limits
            formatted_message = self._truncate_if_needed(formatted_message)
            
            # Clean up HTML formatting
            formatted_message = self._clean_html_formatting(formatted_message)
            
            logger.info(f"Content formatted successfully. Length: {len(formatted_message)} characters")
            return formatted_message
            
        except Exception as e:
            logger.error(f"Error formatting content: {str(e)}")
            raise
    
    def _parse_json_content(self, content: str) -> Dict[str, Any]:
        """
        Parse JSON content from string, handling potential formatting issues.
        
        Args:
            content: JSON string content
            
        Returns:
            Parsed content dictionary
        """
        try:
            # Remove any potential markdown code blocks
            content = re.sub(r'```json\s*', '', content)
            content = re.sub(r'```\s*$', '', content)
            
            # Parse JSON
            return json.loads(content)
            
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON content: {str(e)}")
            # Return a simple structure if parsing fails
            return {"content": content, "title": "Cosmic Fact"}
    
    def _build_message(self, content_data: Dict[str, Any]) -> str:
        """
        Build the formatted message from content data.
        
        Args:
            content_data: Dictionary containing content elements
            
        Returns:
            Formatted message string
        """
        message_parts = []
        
        # Add title if present
        if "title" in content_data and content_data["title"]:
            title = content_data["title"].strip()
            message_parts.append(f"<b>{title}</b>\n")
        
        # Add main content
        if "content" in content_data and content_data["content"]:
            content = content_data["content"].strip()
            message_parts.append(f"{content}\n")
        
        # Add additional sections if present
        for key in ["explanation", "fact", "myth_buster", "mystery", "scenario", "question"]:
            if key in content_data and content_data[key]:
                section_content = content_data[key].strip()
                message_parts.append(f"{section_content}\n")
        
        # Add hashtags if present
        if "hashtags" in content_data and content_data["hashtags"]:
            hashtags = content_data["hashtags"]
            if isinstance(hashtags, list):
                hashtags_str = " ".join(hashtags)
            else:
                hashtags_str = str(hashtags)
            message_parts.append(f"\n{hashtags_str}")
        
        return "\n".join(message_parts).strip()
    
    def _truncate_if_needed(self, message: str) -> str:
        """
        Truncate message if it exceeds Telegram limits.
        
        Args:
            message: Message to check and truncate
            
        Returns:
            Truncated message if needed
        """
        if len(message) <= self.MAX_MESSAGE_LENGTH:
            return message
        
        logger.warning(f"Message length ({len(message)}) exceeds Telegram limit. Truncating...")
        
        # Truncate and add ellipsis
        truncated = message[:self.MAX_MESSAGE_LENGTH - 10] + "..."
        return truncated
    
    def _clean_html_formatting(self, message: str) -> str:
        """
        Clean and validate HTML formatting for Telegram.

        Args:
            message: Message with HTML formatting

        Returns:
            Cleaned message with valid Telegram HTML
        """
        # Remove unsupported HTML tags and keep only Telegram-supported ones
        # Telegram supports: <b>, <strong>, <i>, <em>, <u>, <ins>, <s>, <strike>, <del>, <code>, <pre>

        # Remove any HTML document structure tags that might be generated by AI
        message = re.sub(r'</?html[^>]*>', '', message, flags=re.IGNORECASE)
        message = re.sub(r'</?body[^>]*>', '', message, flags=re.IGNORECASE)
        message = re.sub(r'</?head[^>]*>', '', message, flags=re.IGNORECASE)
        message = re.sub(r'</?title[^>]*>', '', message, flags=re.IGNORECASE)
        message = re.sub(r'</?meta[^>]*>', '', message, flags=re.IGNORECASE)
        message = re.sub(r'</?div[^>]*>', '', message, flags=re.IGNORECASE)
        message = re.sub(r'</?p[^>]*>', '', message, flags=re.IGNORECASE)
        message = re.sub(r'</?span[^>]*>', '', message, flags=re.IGNORECASE)
        message = re.sub(r'</?h[1-6][^>]*>', '', message, flags=re.IGNORECASE)

        # Replace common markdown-style formatting that might slip through
        message = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', message)  # Bold
        message = re.sub(r'\*(.*?)\*', r'<i>\1</i>', message)      # Italic
        message = re.sub(r'`(.*?)`', r'<code>\1</code>', message)  # Code

        # Remove any remaining markdown formatting
        message = re.sub(r'[_~]', '', message)

        # Clean up extra whitespace
        message = re.sub(r'\n\s*\n', '\n\n', message)  # Multiple newlines
        message = message.strip()

        return message


def format_for_telegram(content: Dict[str, Any]) -> str:
    """
    Convenience function to format content for Telegram.
    
    Args:
        content: Content dictionary to format
        
    Returns:
        Formatted content string
    """
    formatter = TelegramFormatter()
    return formatter.format_content(content)
