Metadata-Version: 2.1
Name: google-generativeai
Version: 0.8.3
Summary: Google Generative AI High level API client library and tools.
Home-page: https://github.com/google/generative-ai-python
Author: Google LLC
Author-email: <EMAIL>
License: Apache 2.0
Platform: Posix; MacOS X; Windows
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Operating System :: OS Independent
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: google-ai-generativelanguage ==0.6.10
Requires-Dist: google-api-core
Requires-Dist: google-api-python-client
Requires-Dist: google-auth >=2.15.0
Requires-Dist: protobuf
Requires-Dist: pydantic
Requires-Dist: tqdm
Requires-Dist: typing-extensions
Provides-Extra: dev
Requires-Dist: absl-py ; extra == 'dev'
Requires-Dist: black ; extra == 'dev'
Requires-Dist: nose2 ; extra == 'dev'
Requires-Dist: pandas ; extra == 'dev'
Requires-Dist: pytype ; extra == 'dev'
Requires-Dist: pyyaml ; extra == 'dev'
Requires-Dist: Pillow ; extra == 'dev'
Requires-Dist: ipython ; extra == 'dev'

# Google AI Python SDK for the Gemini API

[![PyPI version](https://badge.fury.io/py/google-generativeai.svg)](https://badge.fury.io/py/google-generativeai)
![Python support](https://img.shields.io/pypi/pyversions/google-generativeai)
![PyPI - Downloads](https://img.shields.io/pypi/dd/google-generativeai)

The Google AI Python SDK is the easiest way for Python developers to build with the Gemini API. The Gemini API gives you access to Gemini [models](https://ai.google.dev/models/gemini) created by [Google DeepMind](https://deepmind.google/technologies/gemini/#introduction). Gemini models are built from the ground up to be multimodal, so you can reason seamlessly across text, images, and code. 

## Get started with the Gemini API
1. Go to [Google AI Studio](https://aistudio.google.com/).
2. Login with your Google account.
3. [Create](https://aistudio.google.com/app/apikey) an API key.
4. Try a Python SDK [quickstart](https://github.com/google-gemini/gemini-api-cookbook/blob/main/quickstarts/Prompting.ipynb) in the [Gemini API Cookbook](https://github.com/google-gemini/gemini-api-cookbook/).
5. For detailed instructions, try the 
[Python SDK tutorial](https://ai.google.dev/tutorials/python_quickstart) on [ai.google.dev](https://ai.google.dev).

## Usage example
See the [Gemini API Cookbook](https://github.com/google-gemini/gemini-api-cookbook/) or [ai.google.dev](https://ai.google.dev) for complete code.

1. Install from [PyPI](https://pypi.org/project/google-generativeai).

`pip install -U google-generativeai`

2. Import the SDK and configure your API key.

```python
import google.generativeai as genai
import os

genai.configure(api_key=os.environ["GEMINI_API_KEY"])
```

3. Create a model and run a prompt.

```python
model = genai.GenerativeModel('gemini-1.5-flash')
response = model.generate_content("The opposite of hot is")
print(response.text)
```

## Documentation

See the [Gemini API Cookbook](https://github.com/google-gemini/gemini-api-cookbook/) or [ai.google.dev](https://ai.google.dev) for complete documentation.

## Contributing

See [Contributing](https://github.com/google/generative-ai-python/blob/main/CONTRIBUTING.md) for more information on contributing to the Google AI Python SDK.

## License

The contents of this repository are licensed under the [Apache License, version 2.0](http://www.apache.org/licenses/LICENSE-2.0).
