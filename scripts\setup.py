#!/usr/bin/env python3
"""
Setup script for CosmicFacts application.
Automates the initial setup and configuration process.
"""

import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("✗ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def create_virtual_environment():
    """Create a virtual environment."""
    if Path("venv").exists():
        print("✓ Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")


def install_dependencies():
    """Install required dependencies."""
    # Determine the correct pip path based on OS
    if os.name == 'nt':  # Windows
        pip_path = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        pip_path = "venv/bin/pip"
    
    return run_command(f"{pip_path} install -r requirements.txt", "Installing dependencies")


def create_directories():
    """Create necessary directories."""
    directories = ["logs", "src/data/prompts"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    return True


def check_env_file():
    """Check if .env file exists and has required variables."""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("✗ .env file not found")
        print("Please create a .env file with the following variables:")
        print("TELEGRAM_BOT_TOKEN=your_bot_token")
        print("TELEGRAM_CHANNEL_ID=your_channel_id")
        print("GEMINI_KEY=your_gemini_api_key")
        return False
    
    # Check for required variables
    required_vars = ["TELEGRAM_BOT_TOKEN", "TELEGRAM_CHANNEL_ID", "GEMINI_KEY"]
    env_content = env_file.read_text()
    
    missing_vars = []
    for var in required_vars:
        if var not in env_content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"✗ Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✓ .env file found with required variables")
    return True


def test_installation():
    """Test the installation by running connection tests."""
    print("\nTesting installation...")
    
    # Determine the correct python path based on OS
    if os.name == 'nt':  # Windows
        python_path = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        python_path = "venv/bin/python"
    
    return run_command(f"{python_path} app.py --action test", "Testing connections")


def main():
    """Main setup function."""
    print("CosmicFacts Setup Script")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create necessary directories
    if not create_directories():
        sys.exit(1)
    
    # Check environment file
    if not check_env_file():
        print("\nSetup completed with warnings. Please configure your .env file.")
        sys.exit(1)
    
    # Test installation
    if test_installation():
        print("\n✓ Setup completed successfully!")
        print("\nYou can now run the application with:")
        if os.name == 'nt':
            print("venv\\Scripts\\python app.py")
        else:
            print("venv/bin/python app.py")
    else:
        print("\n⚠ Setup completed but connection tests failed.")
        print("Please check your .env configuration and try again.")


if __name__ == "__main__":
    main()
