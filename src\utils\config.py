"""
Configuration module for loading environment variables and application settings.
"""

import os
from dotenv import load_dotenv
from typing import Optional


class Config:
    """Configuration class for managing environment variables and settings."""
    
    def __init__(self):
        """Initialize configuration by loading environment variables."""
        load_dotenv()
        self._validate_required_vars()
    
    @property
    def telegram_bot_token(self) -> str:
        """Get Telegram bot token from environment variables."""
        return os.getenv('TELEGRAM_BOT_TOKEN', '')
    
    @property
    def telegram_channel_id(self) -> str:
        """Get Telegram channel ID from environment variables."""
        return os.getenv('TELEGRAM_CHANNEL_ID', '')
    
    @property
    def gemini_api_key(self) -> str:
        """Get Gemini API key from environment variables."""
        return os.getenv('GEMINI_KEY', '')
    
    @property
    def gemini_model(self) -> str:
        """Get Gemini model name from environment variables."""
        return os.getenv('GEMINI_MODEL', 'gemini-2.0-flash')
    
    @property
    def log_level(self) -> str:
        """Get logging level from environment variables."""
        return os.getenv('LOG_LEVEL', 'INFO')
    
    @property
    def max_retries(self) -> int:
        """Get maximum number of API retries from environment variables."""
        return int(os.getenv('MAX_RETRIES', '3'))
    
    @property
    def retry_delay(self) -> int:
        """Get retry delay in seconds from environment variables."""
        return int(os.getenv('RETRY_DELAY', '5'))
    
    def _validate_required_vars(self) -> None:
        """Validate that all required environment variables are set."""
        required_vars = [
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHANNEL_ID',
            'GEMINI_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")


# Global configuration instance
config = Config()
