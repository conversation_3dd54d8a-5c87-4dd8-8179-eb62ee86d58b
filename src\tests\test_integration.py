"""
Integration tests for the enhanced CosmicFacts content generation system.
Tests the complete end-to-end workflow including topic selection, content generation, and formatting.
"""

import pytest
import json
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add src to path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from data.topic_manager import TopicManager
from data.prompts_engine import PromptEngineFactory
from core.enhanced_content_generator import EnhancedContentGenerator
from core.scheduler import ContentScheduler
from utils.formatters import TelegramFormatter


class TestTopicManagerIntegration:
    """Test topic manager integration with CSV files."""
    
    def test_topic_manager_initialization(self):
        """Test that topic manager initializes correctly with CSV files."""
        tm = TopicManager()
        
        # Check that CSV files exist
        topics_dir = Path("src/data/topics")
        assert topics_dir.exists()
        
        expected_files = [
            "cosmic_fact.csv", "myth_buster.csv", "explainer.csv",
            "space_history.csv", "cosmic_mystery.csv", 
            "what_if_scenario.csv", "audience_question.csv"
        ]
        
        for filename in expected_files:
            csv_file = topics_dir / filename
            assert csv_file.exists(), f"Missing CSV file: {filename}"
    
    def test_topic_selection_from_csv(self):
        """Test that topics are properly selected from CSV files."""
        tm = TopicManager()
        
        for content_type in ["cosmic_fact", "myth_buster", "explainer"]:
            topic = tm.get_topic(content_type)
            assert topic is not None
            assert isinstance(topic, str)
            assert len(topic) > 0
    
    def test_topic_usage_tracking(self):
        """Test that topic usage is properly tracked."""
        tm = TopicManager()
        
        # Get a topic and verify it's marked as used
        topic1 = tm.get_topic("cosmic_fact")
        topic2 = tm.get_topic("cosmic_fact")
        
        # Topics should be different (unless we've exhausted the list)
        stats = tm.get_topic_stats()
        assert stats["cosmic_fact"]["used"] >= 1
    
    def test_topic_statistics(self):
        """Test topic statistics functionality."""
        tm = TopicManager()
        
        stats = tm.get_topic_stats()
        
        # Check structure
        assert isinstance(stats, dict)
        for content_type in ["cosmic_fact", "myth_buster", "explainer"]:
            assert content_type in stats
            assert "total" in stats[content_type]
            assert "used" in stats[content_type]
            assert "remaining" in stats[content_type]
            assert stats[content_type]["total"] > 0


class TestPromptEngineIntegration:
    """Test prompt engine integration."""
    
    def test_prompt_engine_factory(self):
        """Test that prompt engine factory creates correct engines."""
        for content_type in ["cosmic_fact", "myth_buster", "explainer"]:
            engine = PromptEngineFactory.get_engine(content_type)
            assert engine is not None
            assert hasattr(engine, 'generate_stage1_prompt')
            assert hasattr(engine, 'generate_stage2_prompt')
    
    def test_stage1_prompt_generation(self):
        """Test Stage 1 prompt generation."""
        engine = PromptEngineFactory.get_engine("cosmic_fact")
        
        topic = "Black holes and event horizons"
        prompt = engine.generate_stage1_prompt(topic)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 100
        assert topic in prompt
        assert "JSON" in prompt
    
    def test_stage2_prompt_generation(self):
        """Test Stage 2 prompt generation."""
        engine = PromptEngineFactory.get_engine("cosmic_fact")
        
        raw_content = '{"title": "Test", "content": "Test content"}'
        prompt = engine.generate_stage2_prompt(raw_content)
        
        assert isinstance(prompt, str)
        assert len(prompt) > 100
        assert "humanization" in prompt.lower()
        assert raw_content in prompt


class TestEnhancedContentGeneratorIntegration:
    """Test enhanced content generator integration."""
    
    @pytest.fixture
    def mock_gemini_response(self):
        """Mock Gemini API response."""
        return '''
        {
            "title": "Amazing Black Hole Discovery",
            "content": "Scientists have discovered that <b>black holes</b> are even more fascinating than we thought. These cosmic monsters can bend space and time in ways that challenge our understanding of physics.",
            "hashtags": ["#blackholes", "#space", "#astronomy", "#physics", "#cosmicfacts"]
        }
        '''
    
    @patch('src.core.enhanced_content_generator.genai.configure')
    @patch('src.core.enhanced_content_generator.genai.GenerativeModel')
    def test_content_generation_workflow(self, mock_model_class, mock_configure, mock_gemini_response):
        """Test complete content generation workflow."""
        # Setup mocks
        mock_model = Mock()
        mock_response = Mock()
        mock_response.text = mock_gemini_response
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        # Test content generation
        generator = EnhancedContentGenerator()
        content = generator.generate_content("cosmic_fact")
        
        # Verify structure
        assert isinstance(content, dict)
        assert "title" in content
        assert "content" in content
        assert "hashtags" in content
        assert "metadata" in content
        
        # Verify metadata
        metadata = content["metadata"]
        assert metadata["content_type"] == "cosmic_fact"
        assert metadata["generation_stages"] == 2
        assert "topic" in metadata
    
    @patch('src.core.enhanced_content_generator.genai.configure')
    @patch('src.core.enhanced_content_generator.genai.GenerativeModel')
    def test_content_quality_validation(self, mock_model_class, mock_configure, mock_gemini_response):
        """Test content quality validation."""
        # Setup mocks
        mock_model = Mock()
        mock_response = Mock()
        mock_response.text = mock_gemini_response
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        generator = EnhancedContentGenerator()
        
        # Test valid content (updated for new length requirements)
        valid_content = {
            "title": "Test Title",
            "content": "This is a test content that is long enough to pass validation and contains proper information about space and astronomy. It needs to be between 150-650 characters to pass the new validation requirements.",
            "hashtags": ["#space", "#test", "#astronomy"]
        }

        assert generator.validate_content_quality(valid_content) == True
        
        # Test invalid content (too short)
        invalid_content = {
            "title": "Short",
            "content": "Too short",
            "hashtags": ["#test"]
        }
        
        assert generator.validate_content_quality(invalid_content) == False
    
    @patch('src.core.enhanced_content_generator.genai.configure')
    @patch('src.core.enhanced_content_generator.genai.GenerativeModel')
    def test_content_enhancement(self, mock_model_class, mock_configure):
        """Test content quality enhancement."""
        generator = EnhancedContentGenerator()
        
        # Test content with issues
        content_with_issues = {
            "title": "test title",  # No punctuation
            "content": "  Test content with  extra   spaces  ",  # Extra spaces
            "hashtags": ["space", "#astronomy", "  #physics  "]  # Mixed formatting
        }
        
        enhanced = generator.enhance_content_quality(content_with_issues)
        
        # Check enhancements
        assert enhanced["title"].endswith("!")
        assert "  " not in enhanced["content"]  # No double spaces
        assert all(tag.startswith("#") for tag in enhanced["hashtags"])
        assert all(tag == tag.strip().lower() for tag in enhanced["hashtags"])


class TestSchedulerIntegration:
    """Test scheduler integration."""
    
    def test_scheduler_initialization(self):
        """Test scheduler initialization."""
        scheduler = ContentScheduler()
        
        # Test that it returns valid content types
        content_type = scheduler.get_today_content_type()
        assert content_type in [
            "cosmic_fact", "myth_buster", "explainer", "space_history",
            "cosmic_mystery", "what_if_scenario", "audience_question"
        ]
    
    def test_weekly_schedule_consistency(self):
        """Test that weekly schedule is consistent."""
        scheduler = ContentScheduler()
        
        # Test all days of the week
        for day in range(7):
            content_type = scheduler.get_content_type_for_day(day)
            assert content_type in [
                "cosmic_fact", "myth_buster", "explainer", "space_history",
                "cosmic_mystery", "what_if_scenario", "audience_question"
            ]


class TestTelegramFormatterIntegration:
    """Test Telegram formatter integration."""
    
    def test_content_formatting(self):
        """Test content formatting for Telegram."""
        formatter = TelegramFormatter()
        
        content_data = {
            "title": "Amazing Space Discovery",
            "content": "Scientists have found that <b>black holes</b> are incredible objects that warp spacetime.",
            "hashtags": ["#space", "#blackholes", "#astronomy"]
        }
        
        formatted = formatter.format_content(content_data)
        
        assert isinstance(formatted, str)
        assert "Amazing Space Discovery" in formatted
        assert "<b>black holes</b>" in formatted
        assert "#space #blackholes #astronomy" in formatted
    
    def test_html_cleaning(self):
        """Test HTML cleaning functionality."""
        formatter = TelegramFormatter()
        
        content_with_bad_html = {
            "title": "Test Title",
            "content": "<html><body><div>Content with <b>good</b> and <div>bad</div> HTML</div></body></html>",
            "hashtags": ["#test"]
        }
        
        formatted = formatter.format_content(content_with_bad_html)
        
        # Should remove document structure tags but keep formatting tags
        assert "<html>" not in formatted
        assert "<body>" not in formatted
        assert "<div>" not in formatted
        assert "<b>good</b>" in formatted


class TestEndToEndWorkflow:
    """Test complete end-to-end workflow."""
    
    @patch('src.core.enhanced_content_generator.genai.configure')
    @patch('src.core.enhanced_content_generator.genai.GenerativeModel')
    def test_complete_workflow(self, mock_model_class, mock_configure):
        """Test complete content generation workflow."""
        # Setup mocks
        mock_model = Mock()
        mock_response = Mock()
        mock_response.text = '''
        {
            "title": "Incredible Cosmic Discovery",
            "content": "Recent observations have revealed that <b>neutron stars</b> are even more extreme than previously thought. These incredibly dense objects pack more mass than our Sun into a sphere just 20 kilometers across.",
            "hashtags": ["#neutronstars", "#space", "#astronomy", "#physics", "#cosmicfacts"]
        }
        '''
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        # Initialize components
        generator = EnhancedContentGenerator()
        formatter = TelegramFormatter()
        
        # Generate content
        content = generator.generate_content_with_validation("cosmic_fact")
        
        # Format for Telegram
        formatted_message = formatter.format_content(content)
        
        # Verify complete workflow
        assert isinstance(content, dict)
        assert "title" in content
        assert "content" in content
        assert "hashtags" in content
        assert "metadata" in content
        
        assert isinstance(formatted_message, str)
        assert len(formatted_message) > 0
        assert content["title"] in formatted_message
        
        # Verify topic was used
        content_stats = generator.get_content_statistics()
        assert content_stats["total_used"] > 0
    
    @patch('src.core.enhanced_content_generator.genai.configure')
    @patch('src.core.enhanced_content_generator.genai.GenerativeModel')
    def test_bulk_generation_workflow(self, mock_model_class, mock_configure):
        """Test bulk content generation workflow."""
        # Setup mocks
        mock_model = Mock()
        mock_response = Mock()
        mock_response.text = '''
        {
            "title": "Test Bulk Content",
            "content": "This is test content for bulk generation testing. It contains enough text to pass validation requirements.",
            "hashtags": ["#test", "#bulk", "#space"]
        }
        '''
        mock_model.generate_content.return_value = mock_response
        mock_model_class.return_value = mock_model
        
        generator = EnhancedContentGenerator()
        
        # Test bulk generation
        requests = [
            {"content_type": "cosmic_fact"},
            {"content_type": "myth_buster"},
            {"content_type": "explainer"}
        ]
        
        results = generator.bulk_generate_content(requests)
        
        # Verify results
        assert len(results) == 3
        for result in results:
            assert "title" in result
            assert "content" in result
            assert "request_metadata" in result
            assert result["request_metadata"]["bulk_generation"] == True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
