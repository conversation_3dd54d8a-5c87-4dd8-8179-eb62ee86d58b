"""
Poll generator for Sunday audience engagement content.
Creates space-themed polls with questions and multiple choice options.
"""

import json
import random
from typing import Dict, Any, List
from pathlib import Path

from src.utils.logger import get_logger
from src.data.topic_manager import TopicManager
from src.data.prompts_engine import PromptEngineFactory

logger = get_logger(__name__)


class PollGenerator:
    """Generate engaging space-themed polls for audience interaction."""
    
    def __init__(self):
        """Initialize the poll generator."""
        self.topic_manager = TopicManager()
        self.polls_dir = Path("src/data/polls")
        self.polls_dir.mkdir(exist_ok=True)
        
        # Create poll topics if they don't exist
        self._create_poll_topics()
    
    def generate_poll(self, custom_topic: str = None) -> Dict[str, Any]:
        """
        Generate a space-themed poll with question and options.
        
        Args:
            custom_topic: Optional custom topic for the poll
            
        Returns:
            Dictionary containing poll question and options
        """
        try:
            # Get topic for poll
            if custom_topic:
                topic = custom_topic
                logger.info(f"Using custom poll topic: {topic}")
            else:
                # Use audience_question topics for polls
                topic = self.topic_manager.get_topic("audience_question")
                if not topic:
                    topic = self._get_fallback_poll_topic()
                logger.info(f"Selected poll topic: {topic}")
            
            # Generate poll using predefined templates or AI
            poll_data = self._generate_poll_from_topic(topic)
            
            # Validate poll structure
            if self._validate_poll(poll_data):
                logger.info("Poll generated successfully")
                return poll_data
            else:
                logger.warning("Generated poll failed validation, using fallback")
                return self._get_fallback_poll()
                
        except Exception as e:
            logger.error(f"Error generating poll: {str(e)}")
            return self._get_fallback_poll()
    
    def _generate_poll_from_topic(self, topic: str) -> Dict[str, Any]:
        """Generate poll from topic using templates."""
        
        # Space-themed poll templates
        poll_templates = [
            {
                "question": f"What fascinates you most about {topic.lower()}?",
                "options": [
                    "The scientific discovery aspect",
                    "The mind-blowing scale",
                    "The potential for life",
                    "The beautiful imagery"
                ]
            },
            {
                "question": f"If you could learn one thing about {topic.lower()}, what would it be?",
                "options": [
                    "How it was discovered",
                    "What it means for humanity",
                    "The physics behind it",
                    "Future exploration plans"
                ]
            },
            {
                "question": f"What's your reaction when you think about {topic.lower()}?",
                "options": [
                    "Pure wonder and amazement",
                    "Curiosity to learn more",
                    "Excitement about the future",
                    "Humbling perspective on life"
                ]
            }
        ]
        
        # Select random template
        template = random.choice(poll_templates)
        
        # Customize based on topic
        customized_poll = self._customize_poll_for_topic(template, topic)
        
        return {
            "question": customized_poll["question"],
            "options": customized_poll["options"],
            "allows_multiple_answers": False,
            "is_anonymous": True,
            "type": "regular",
            "metadata": {
                "topic": topic,
                "content_type": "audience_poll",
                "generated_for": "sunday_engagement"
            }
        }
    
    def _customize_poll_for_topic(self, template: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """Customize poll template based on specific topic."""
        
        # Topic-specific customizations
        topic_lower = topic.lower()
        
        if "black hole" in topic_lower:
            return {
                "question": "What amazes you most about black holes?",
                "options": [
                    "They bend space and time",
                    "Nothing can escape them",
                    "They're invisible monsters",
                    "They might connect universes"
                ]
            }
        elif "mars" in topic_lower:
            return {
                "question": "What excites you most about Mars exploration?",
                "options": [
                    "Finding signs of ancient life",
                    "Human missions to Mars",
                    "Terraforming possibilities",
                    "The rovers' discoveries"
                ]
            }
        elif "galaxy" in topic_lower or "milky way" in topic_lower:
            return {
                "question": "What's most mind-blowing about our galaxy?",
                "options": [
                    "400 billion stars",
                    "The supermassive black hole center",
                    "We're spinning through space",
                    "It will collide with Andromeda"
                ]
            }
        elif "alien" in topic_lower or "extraterrestrial" in topic_lower:
            return {
                "question": "What do you think about alien life?",
                "options": [
                    "It definitely exists somewhere",
                    "We might be alone",
                    "They've already visited Earth",
                    "We'll find it in our lifetime"
                ]
            }
        elif "universe" in topic_lower:
            return {
                "question": "What's the most mind-bending fact about the universe?",
                "options": [
                    "It's 13.8 billion years old",
                    "It's expanding faster and faster",
                    "Most of it is invisible",
                    "It might be infinite"
                ]
            }
        else:
            # Use template as-is for other topics
            return template
    
    def _validate_poll(self, poll_data: Dict[str, Any]) -> bool:
        """Validate poll structure and content."""
        try:
            # Check required fields
            required_fields = ["question", "options"]
            for field in required_fields:
                if field not in poll_data or not poll_data[field]:
                    logger.warning(f"Missing required poll field: {field}")
                    return False
            
            # Check question length (Telegram limit: 300 characters)
            question = poll_data["question"]
            if len(question) > 300:
                logger.warning(f"Poll question too long: {len(question)} characters")
                return False
            
            # Check options
            options = poll_data["options"]
            if not isinstance(options, list):
                logger.warning("Poll options must be a list")
                return False
            
            if len(options) < 2 or len(options) > 10:
                logger.warning(f"Poll must have 2-10 options, got {len(options)}")
                return False
            
            # Check option lengths (Telegram limit: 100 characters each)
            for i, option in enumerate(options):
                if len(option) > 100:
                    logger.warning(f"Poll option {i+1} too long: {len(option)} characters")
                    return False
            
            logger.info("Poll validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error validating poll: {str(e)}")
            return False
    
    def _get_fallback_poll(self) -> Dict[str, Any]:
        """Get a fallback poll when generation fails."""
        return {
            "question": "What's your favorite thing about space exploration?",
            "options": [
                "Discovering new worlds",
                "Understanding our universe",
                "The amazing technology",
                "Inspiring future generations"
            ],
            "allows_multiple_answers": False,
            "is_anonymous": True,
            "type": "regular",
            "metadata": {
                "topic": "space exploration",
                "content_type": "audience_poll",
                "generated_for": "sunday_engagement",
                "fallback": True
            }
        }
    
    def _get_fallback_poll_topic(self) -> str:
        """Get a fallback topic when no topics are available."""
        fallback_topics = [
            "space exploration",
            "the universe",
            "alien life",
            "black holes",
            "Mars exploration",
            "the Milky Way galaxy"
        ]
        return random.choice(fallback_topics)
    
    def _create_poll_topics(self) -> None:
        """Create poll-specific topics if needed."""
        # Poll topics are derived from audience_question topics
        # This method can be extended to create poll-specific topic files
        pass
    
    def get_poll_statistics(self) -> Dict[str, Any]:
        """Get statistics about poll generation."""
        try:
            # Get audience question stats since polls use those topics
            topic_stats = self.topic_manager.get_topic_stats()
            audience_stats = topic_stats.get("audience_question", {})
            
            return {
                "poll_topics_available": audience_stats.get("total", 0),
                "poll_topics_used": audience_stats.get("used", 0),
                "poll_topics_remaining": audience_stats.get("remaining", 0),
                "poll_generation_ready": audience_stats.get("remaining", 0) > 0
            }
            
        except Exception as e:
            logger.error(f"Error getting poll statistics: {str(e)}")
            return {"error": str(e)}


def create_sample_polls() -> List[Dict[str, Any]]:
    """Create sample polls for testing."""
    sample_polls = [
        {
            "question": "What's the most mind-blowing fact about black holes?",
            "options": [
                "Time slows down near them",
                "They can evaporate over time",
                "They might connect to other universes",
                "They're invisible but detectable"
            ],
            "allows_multiple_answers": False,
            "is_anonymous": True,
            "type": "regular"
        },
        {
            "question": "Which space mission excites you most?",
            "options": [
                "James Webb Space Telescope",
                "Mars Sample Return",
                "Europa Clipper",
                "Artemis Moon Program"
            ],
            "allows_multiple_answers": False,
            "is_anonymous": True,
            "type": "regular"
        },
        {
            "question": "What do you think we'll discover first?",
            "options": [
                "Life on Mars",
                "Life in Europa's ocean",
                "Alien radio signals",
                "Habitable exoplanets"
            ],
            "allows_multiple_answers": False,
            "is_anonymous": True,
            "type": "regular"
        }
    ]
    
    return sample_polls


if __name__ == "__main__":
    # Test poll generation
    generator = PollGenerator()
    
    # Generate a few test polls
    for i in range(3):
        poll = generator.generate_poll()
        print(f"\n--- Poll {i+1} ---")
        print(f"Question: {poll['question']}")
        print("Options:")
        for j, option in enumerate(poll['options'], 1):
            print(f"  {j}. {option}")
