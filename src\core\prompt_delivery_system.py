"""
Prompt delivery system for sending image generation prompts to Telegram users.
Sends detailed image prompts and files to specified user for manual image creation.
"""

import requests
from typing import Dict, Any, Optional
from pathlib import Path

from src.utils.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)


class PromptDeliverySystem:
    """Handle delivery of image prompts to Telegram users."""
    
    def __init__(self):
        """Initialize the prompt delivery system."""
        self.bot_token = config.telegram_bot_token
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        
        if not self.bot_token:
            logger.error("Telegram bot token not configured")
            raise ValueError("Telegram bot token is required")
        
        logger.info("Prompt delivery system initialized")
    
    def send_image_prompt_to_user(self, user_id: int, prompt_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send image generation prompt to specified user.
        
        Args:
            user_id: Telegram user ID to send prompt to
            prompt_data: Dictionary containing prompt and metadata
            
        Returns:
            Dictionary with delivery result
        """
        try:
            logger.info(f"Sending image prompt to user {user_id}")
            
            # Format the message
            message_text = self._format_prompt_message(prompt_data)
            
            # Send text message with prompt
            text_result = self._send_text_message(user_id, message_text)
            
            # Send prompt file if available
            file_result = None
            if prompt_data.get("prompt_file_path"):
                file_result = self._send_document(user_id, prompt_data["prompt_file_path"])
            
            return {
                "success": text_result.get("ok", False),
                "text_message_result": text_result,
                "file_result": file_result,
                "user_id": user_id,
                "content_title": prompt_data.get("content_title", ""),
                "content_type": prompt_data.get("content_type", "")
            }
            
        except Exception as e:
            logger.error(f"Error sending prompt to user {user_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id
            }
    
    def _format_prompt_message(self, prompt_data: Dict[str, Any]) -> str:
        """Format the prompt message for Telegram."""
        
        content_title = prompt_data.get("content_title", "Untitled")
        content_type = prompt_data.get("content_type", "content")
        image_prompt = prompt_data.get("image_prompt", "No prompt available")
        
        # Content type emojis
        type_emojis = {
            "myth_buster": "❌🔍",
            "explainer": "📊📚", 
            "space_history": "🚀📜"
        }
        
        emoji = type_emojis.get(content_type, "🖼️")
        
        message_parts = [
            f"{emoji} <b>IMAGE PROMPT GENERATED</b>",
            "",
            f"<b>Content Type:</b> {content_type.replace('_', ' ').title()}",
            f"<b>Title:</b> {content_title}",
            "",
            "🎨 <b>DETAILED IMAGE GENERATION PROMPT:</b>",
            "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            "",
            f"<code>{image_prompt}</code>",
            "",
            "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
            "",
            "📋 <b>INSTRUCTIONS:</b>",
            "1. Copy the prompt above",
            "2. Use with AI image generator (DALL-E, Midjourney, Stable Diffusion)",
            "3. Generate the image",
            "4. Manually post the image to the Telegram channel",
            "5. Post the image AFTER the text content",
            "",
            "💡 <i>A text file with the prompt is also being sent for easy copying.</i>",
            "",
            f"#{content_type} #ImagePrompt #ManualPosting"
        ]
        
        return "\n".join(message_parts)
    
    def _send_text_message(self, chat_id: int, text: str) -> Dict[str, Any]:
        """Send a text message via Telegram API."""
        try:
            url = f"{self.base_url}/sendMessage"
            
            # Split message if too long (Telegram limit: 4096 characters)
            if len(text) > 4000:
                # Send in parts
                parts = self._split_message(text, 4000)
                results = []
                
                for i, part in enumerate(parts):
                    params = {
                        "chat_id": chat_id,
                        "text": f"📄 <b>Part {i+1}/{len(parts)}</b>\n\n{part}",
                        "parse_mode": "HTML"
                    }
                    
                    response = requests.post(url, data=params, timeout=30)
                    result = response.json()
                    results.append(result)
                
                # Return result of first part
                return results[0] if results else {"ok": False, "error": "No parts sent"}
            else:
                params = {
                    "chat_id": chat_id,
                    "text": text,
                    "parse_mode": "HTML"
                }
                
                response = requests.post(url, data=params, timeout=30)
                result = response.json()
            
            if result.get("ok"):
                logger.info(f"Prompt message sent successfully to chat {chat_id}")
            else:
                logger.error(f"Failed to send prompt message: {result.get('description', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error sending text message: {str(e)}")
            return {"ok": False, "error": str(e)}
    
    def _send_document(self, chat_id: int, file_path: str) -> Dict[str, Any]:
        """Send a document file via Telegram API."""
        try:
            url = f"{self.base_url}/sendDocument"
            
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                logger.warning(f"Prompt file not found: {file_path}")
                return {"ok": False, "error": "File not found"}
            
            with open(file_path_obj, 'rb') as file:
                files = {'document': file}
                data = {
                    'chat_id': chat_id,
                    'caption': '📄 Image generation prompt file for easy copying'
                }
                
                response = requests.post(url, files=files, data=data, timeout=60)
                result = response.json()
            
            if result.get("ok"):
                logger.info(f"Prompt file sent successfully to chat {chat_id}")
            else:
                logger.error(f"Failed to send prompt file: {result.get('description', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error sending document: {str(e)}")
            return {"ok": False, "error": str(e)}
    
    def _split_message(self, text: str, max_length: int) -> list:
        """Split long message into parts."""
        if len(text) <= max_length:
            return [text]
        
        parts = []
        current_part = ""
        
        lines = text.split('\n')
        
        for line in lines:
            if len(current_part) + len(line) + 1 <= max_length:
                current_part += line + '\n'
            else:
                if current_part:
                    parts.append(current_part.rstrip())
                current_part = line + '\n'
        
        if current_part:
            parts.append(current_part.rstrip())
        
        return parts
    
    def send_prompt_notification(self, user_id: int, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send a simple notification about prompt generation.
        
        Args:
            user_id: Telegram user ID
            content_data: Content data with title and type
            
        Returns:
            Dictionary with result
        """
        try:
            content_title = content_data.get("title", "Untitled")
            content_type = content_data.get("content_type", "content")
            
            message = f"""
🎨 <b>IMAGE PROMPT READY</b>

Content: {content_title}
Type: {content_type.replace('_', ' ').title()}

📋 An image generation prompt has been created for this content.
Check your messages for the detailed prompt and instructions.

#ImagePrompt #ReadyForGeneration
"""
            
            return self._send_text_message(user_id, message.strip())
            
        except Exception as e:
            logger.error(f"Error sending prompt notification: {str(e)}")
            return {"ok": False, "error": str(e)}
    
    def test_delivery_system(self, user_id: int = 8034528128) -> bool:
        """Test the prompt delivery system."""
        try:
            # Test prompt data
            test_prompt_data = {
                "content_title": "Test Image Prompt",
                "content_type": "myth_buster",
                "image_prompt": "A detailed test prompt for image generation with specific technical requirements...",
                "prompt_file_path": None
            }
            
            # Send test prompt
            result = self.send_image_prompt_to_user(user_id, test_prompt_data)
            
            if result.get("success"):
                logger.info("✅ Prompt delivery test successful")
                return True
            else:
                logger.error(f"❌ Prompt delivery test failed: {result.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"Prompt delivery test error: {str(e)}")
            return False


def test_prompt_delivery():
    """Test the prompt delivery system."""
    try:
        delivery_system = PromptDeliverySystem()
        
        print("Testing prompt delivery system...")
        success = delivery_system.test_delivery_system()
        
        if success:
            print("✅ Prompt delivery system test passed!")
        else:
            print("❌ Prompt delivery system test failed!")
        
        return success
        
    except Exception as e:
        print(f"Test failed: {str(e)}")
        return False


if __name__ == "__main__":
    test_prompt_delivery()
