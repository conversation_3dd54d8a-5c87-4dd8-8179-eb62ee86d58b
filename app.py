#!/usr/bin/env python3
"""
CosmicFacts - Automated Content Generation and Telegram Posting System

Main application entry point for generating and posting space-related content
to Telegram channels using Google's Gemini AI API.
"""

import sys
import argparse
from datetime import datetime
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config import config
from src.utils.logger import get_logger
from src.core.enhanced_content_generator import EnhancedContentGenerator
from src.core.telegram_poster import TelegramPoster
from src.core.scheduler import ContentScheduler
from src.core.poll_poster import PollPoster


class CosmicFactsApp:
    """Main application class for CosmicFacts content generation and posting."""
    
    def __init__(self):
        """Initialize the CosmicFacts application."""
        self.logger = get_logger("cosmicfacts.app")
        self.enhanced_content_generator = None
        self.telegram_poster = None
        self.scheduler = None
        
        self.logger.info("CosmicFacts application starting...")
        self._initialize_components()
    
    def _initialize_components(self) -> None:
        """Initialize all application components."""
        try:
            self.logger.info("Initializing application components...")
            
            # Initialize scheduler
            self.scheduler = ContentScheduler()

            # Initialize enhanced content generator
            self.enhanced_content_generator = EnhancedContentGenerator()

            # Initialize Telegram poster
            self.telegram_poster = TelegramPoster()

            # Initialize poll poster
            self.poll_poster = PollPoster()
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {str(e)}")
            raise
    
    def run_daily_post(self) -> bool:
        """
        Generate and post today's content based on the weekly schedule.
        On Sundays, posts polls instead of regular content.

        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info("Starting daily content generation and posting...")

            # Get today's content type
            content_type = self.scheduler.get_today_content_type()

            # Check if it's Sunday (audience_question day) - post poll instead
            from datetime import datetime
            if datetime.now().weekday() == 6 or content_type == "audience_question":
                self.logger.info("Sunday detected - generating and posting poll instead of regular content")
                return self._post_sunday_poll()

            self.logger.info(f"Generating content for: {content_type}")

            # Generate content using enhanced two-stage system
            content_data = self.enhanced_content_generator.generate_content_with_validation(
                content_type=content_type
            )

            # Post to Telegram
            success = self.telegram_poster.post_content_sync(content_data)

            if success:
                self.logger.info("Daily content posted successfully!")
                return True
            else:
                self.logger.error("Failed to post daily content")
                return False

        except Exception as e:
            self.logger.error(f"Error in daily post execution: {str(e)}")
            return False

    def _post_sunday_poll(self) -> bool:
        """
        Generate and post a poll for Sunday engagement.

        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info("Generating Sunday poll...")

            # Generate poll
            poll_data = self.poll_poster.poll_generator.generate_poll()

            # Get channel ID from config
            channel_id = config.telegram_channel_id
            if not channel_id:
                self.logger.error("Telegram channel ID not configured")
                return False

            # Try to send actual poll first
            self.logger.info(f"Attempting to send poll to channel {channel_id}")
            result = self.poll_poster._send_poll_via_api(channel_id, poll_data)

            if result.get("ok"):
                self.logger.info("Sunday poll posted successfully!")
                return True
            else:
                # If poll fails, send as formatted text message
                self.logger.warning(f"Poll posting failed: {result.get('description', 'Unknown error')}")
                self.logger.info("Falling back to poll preview format...")

                preview_text = self.poll_poster._format_poll_as_text(poll_data)
                text_result = self.poll_poster._send_text_message(channel_id, preview_text)

                if text_result.get("ok"):
                    self.logger.info("Sunday poll posted as text preview successfully!")
                    return True
                else:
                    self.logger.error(f"Failed to post poll preview: {text_result.get('description', 'Unknown error')}")
                    return False

        except Exception as e:
            self.logger.error(f"Error posting Sunday poll: {str(e)}")
            return False
    
    def run_custom_post(self, content_type: str, custom_prompt: str = None) -> bool:
        """
        Generate and post content for a specific content type.
        
        Args:
            content_type: Type of content to generate
            custom_prompt: Optional custom prompt to use
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info(f"Starting custom content generation for: {content_type}")
            
            # Prepare context for custom generation
            context = {}
            if custom_prompt:
                context["custom_prompt"] = custom_prompt

            # Generate content using enhanced two-stage system
            content_data = self.enhanced_content_generator.generate_content_with_validation(
                content_type=content_type,
                context=context
            )
            
            # Post to Telegram
            success = self.telegram_poster.post_content_sync(content_data)
            
            if success:
                self.logger.info(f"Custom {content_type} content posted successfully!")
                return True
            else:
                self.logger.error(f"Failed to post custom {content_type} content")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in custom post execution: {str(e)}")
            return False
    
    def test_connections(self) -> bool:
        """
        Test connections to all external services.
        
        Returns:
            True if all connections are successful, False otherwise
        """
        try:
            self.logger.info("Testing connections to external services...")
            
            # Test Telegram connection
            telegram_ok = self.telegram_poster.test_connection_sync()
            
            if telegram_ok:
                self.logger.info("All connection tests passed!")
                return True
            else:
                self.logger.error("Connection tests failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during connection testing: {str(e)}")
            return False
    
    def show_schedule(self) -> None:
        """Display the weekly content schedule."""
        self.logger.info("Weekly Content Schedule:")
        
        days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        
        for i, day in enumerate(days):
            content_type = self.scheduler.get_content_type_for_day(i)
            prompt_config = self.scheduler.get_prompt_for_content_type(content_type)
            description = prompt_config.get("description", "No description available")
            
            print(f"{day}: {content_type.replace('_', ' ').title()} - {description}")

    def send_poll_to_user(self, user_id: int, custom_topic: str = None, preview_mode: bool = False) -> bool:
        """Send a poll to a specific user."""
        try:
            self.logger.info(f"Sending poll to user {user_id}")

            if preview_mode:
                # Send poll as text preview
                result = self.poll_poster.send_poll_preview_to_user(user_id, custom_topic)
            else:
                # Send actual poll
                result = self.poll_poster.send_poll_to_user(user_id, custom_topic)

            if result.get("success"):
                print(f"✅ Poll sent successfully to user {user_id}")
                if "poll_data" in result:
                    poll_data = result["poll_data"]
                    print(f"Question: {poll_data['question']}")
                    print("Options:")
                    for i, option in enumerate(poll_data['options'], 1):
                        print(f"  {i}. {option}")
                return True
            else:
                error_msg = result.get("error", "Unknown error")
                print(f"❌ Failed to send poll to user {user_id}: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"Error sending poll to user {user_id}: {str(e)}")
            print(f"❌ Error sending poll: {str(e)}")
            return False

    def test_poll_generation(self, count: int = 3) -> bool:
        """Test poll generation without sending."""
        try:
            print(f"\n=== TESTING POLL GENERATION ({count} polls) ===")

            test_polls = self.poll_poster.test_poll_generation(count)

            for poll in test_polls:
                if "error" in poll:
                    print(f"\n❌ Poll {poll['poll_number']}: ERROR - {poll['error']}")
                else:
                    print(f"\n✅ Poll {poll['poll_number']}:")
                    print(f"Question: {poll['poll_data']['question']}")
                    print("Options:")
                    for i, option in enumerate(poll['poll_data']['options'], 1):
                        print(f"  {i}. {option}")

            # Show poll statistics
            print(f"\n=== POLL STATISTICS ===")
            stats = self.poll_poster.get_poll_statistics()
            for key, value in stats.items():
                print(f"{key}: {value}")

            return True

        except Exception as e:
            self.logger.error(f"Error testing poll generation: {str(e)}")
            print(f"❌ Poll test failed: {str(e)}")
            return False


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="CosmicFacts - Automated Content Generation and Telegram Posting"
    )
    
    parser.add_argument(
        "--action",
        choices=["daily", "custom", "test", "schedule", "poll", "test-polls"],
        default="daily",
        help="Action to perform (default: daily)"
    )
    
    parser.add_argument(
        "--content-type",
        choices=["cosmic_fact", "myth_buster", "explainer", "space_history", 
                "cosmic_mystery", "what_if_scenario", "audience_question"],
        help="Content type for custom generation"
    )
    
    parser.add_argument(
        "--prompt",
        help="Custom prompt for content generation"
    )

    parser.add_argument(
        "--user-id",
        type=int,
        help="Telegram user ID for poll sending"
    )

    parser.add_argument(
        "--poll-topic",
        help="Custom topic for poll generation"
    )

    parser.add_argument(
        "--preview",
        action="store_true",
        help="Send poll as text preview instead of actual poll"
    )

    parser.add_argument(
        "--count",
        type=int,
        default=3,
        help="Number of test polls to generate"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize application
        app = CosmicFactsApp()
        
        # Execute requested action
        if args.action == "daily":
            success = app.run_daily_post()
            sys.exit(0 if success else 1)
            
        elif args.action == "custom":
            if not args.content_type:
                print("Error: --content-type is required for custom action")
                sys.exit(1)
            
            success = app.run_custom_post(args.content_type, args.prompt)
            sys.exit(0 if success else 1)
            
        elif args.action == "test":
            success = app.test_connections()
            sys.exit(0 if success else 1)
            
        elif args.action == "schedule":
            app.show_schedule()
            sys.exit(0)

        elif args.action == "poll":
            if not args.user_id:
                print("Error: --user-id is required for poll action")
                sys.exit(1)

            success = app.send_poll_to_user(args.user_id, args.poll_topic, args.preview)
            sys.exit(0 if success else 1)

        elif args.action == "test-polls":
            success = app.test_poll_generation(args.count or 3)
            sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Application error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
