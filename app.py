#!/usr/bin/env python3
"""
CosmicFacts - Automated Content Generation and Telegram Posting System

Main application entry point for generating and posting space-related content
to Telegram channels using Google's Gemini AI API.
"""

import sys
import argparse
from datetime import datetime
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config import config
from src.utils.logger import get_logger
from src.core.content_generator import ContentGenerator
from src.core.telegram_poster import TelegramPoster
from src.core.scheduler import ContentScheduler


class CosmicFactsApp:
    """Main application class for CosmicFacts content generation and posting."""
    
    def __init__(self):
        """Initialize the CosmicFacts application."""
        self.logger = get_logger("cosmicfacts.app")
        self.content_generator = None
        self.telegram_poster = None
        self.scheduler = None
        
        self.logger.info("CosmicFacts application starting...")
        self._initialize_components()
    
    def _initialize_components(self) -> None:
        """Initialize all application components."""
        try:
            self.logger.info("Initializing application components...")
            
            # Initialize scheduler
            self.scheduler = ContentScheduler()
            
            # Initialize content generator
            self.content_generator = ContentGenerator()
            
            # Initialize Telegram poster
            self.telegram_poster = TelegramPoster()
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize components: {str(e)}")
            raise
    
    def run_daily_post(self) -> bool:
        """
        Generate and post today's content based on the weekly schedule.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info("Starting daily content generation and posting...")
            
            # Get today's content configuration
            content_type = self.scheduler.get_today_content_type()
            prompt_config = self.scheduler.get_prompt_for_content_type(content_type)
            
            self.logger.info(f"Generating content for: {content_type}")
            
            # Generate content
            content_data = self.content_generator.generate_content(
                prompt=prompt_config["prompt"],
                content_type=content_type
            )
            
            # Post to Telegram
            success = self.telegram_poster.post_content_sync(content_data)
            
            if success:
                self.logger.info("Daily content posted successfully!")
                return True
            else:
                self.logger.error("Failed to post daily content")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in daily post execution: {str(e)}")
            return False
    
    def run_custom_post(self, content_type: str, custom_prompt: str = None) -> bool:
        """
        Generate and post content for a specific content type.
        
        Args:
            content_type: Type of content to generate
            custom_prompt: Optional custom prompt to use
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info(f"Starting custom content generation for: {content_type}")
            
            if custom_prompt:
                prompt = custom_prompt
            else:
                prompt_config = self.scheduler.get_prompt_for_content_type(content_type)
                prompt = prompt_config["prompt"]
            
            # Generate content
            content_data = self.content_generator.generate_content(
                prompt=prompt,
                content_type=content_type
            )
            
            # Post to Telegram
            success = self.telegram_poster.post_content_sync(content_data)
            
            if success:
                self.logger.info(f"Custom {content_type} content posted successfully!")
                return True
            else:
                self.logger.error(f"Failed to post custom {content_type} content")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in custom post execution: {str(e)}")
            return False
    
    def test_connections(self) -> bool:
        """
        Test connections to all external services.
        
        Returns:
            True if all connections are successful, False otherwise
        """
        try:
            self.logger.info("Testing connections to external services...")
            
            # Test Telegram connection
            telegram_ok = self.telegram_poster.test_connection_sync()
            
            if telegram_ok:
                self.logger.info("All connection tests passed!")
                return True
            else:
                self.logger.error("Connection tests failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during connection testing: {str(e)}")
            return False
    
    def show_schedule(self) -> None:
        """Display the weekly content schedule."""
        self.logger.info("Weekly Content Schedule:")
        
        days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
        
        for i, day in enumerate(days):
            content_type = self.scheduler.get_content_type_for_day(i)
            prompt_config = self.scheduler.get_prompt_for_content_type(content_type)
            description = prompt_config.get("description", "No description available")
            
            print(f"{day}: {content_type.replace('_', ' ').title()} - {description}")


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="CosmicFacts - Automated Content Generation and Telegram Posting"
    )
    
    parser.add_argument(
        "--action",
        choices=["daily", "custom", "test", "schedule"],
        default="daily",
        help="Action to perform (default: daily)"
    )
    
    parser.add_argument(
        "--content-type",
        choices=["cosmic_fact", "myth_buster", "explainer", "space_history", 
                "cosmic_mystery", "what_if_scenario", "audience_question"],
        help="Content type for custom generation"
    )
    
    parser.add_argument(
        "--prompt",
        help="Custom prompt for content generation"
    )
    
    args = parser.parse_args()
    
    try:
        # Initialize application
        app = CosmicFactsApp()
        
        # Execute requested action
        if args.action == "daily":
            success = app.run_daily_post()
            sys.exit(0 if success else 1)
            
        elif args.action == "custom":
            if not args.content_type:
                print("Error: --content-type is required for custom action")
                sys.exit(1)
            
            success = app.run_custom_post(args.content_type, args.prompt)
            sys.exit(0 if success else 1)
            
        elif args.action == "test":
            success = app.test_connections()
            sys.exit(0 if success else 1)
            
        elif args.action == "schedule":
            app.show_schedule()
            sys.exit(0)
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Application error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
