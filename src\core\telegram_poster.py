"""
Telegram posting module for automated content publishing.
"""

import async<PERSON>
from typing import Dict, Any, Optional
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError, RetryAfter, TimedOut
from src.utils.config import config
from src.utils.logger import get_logger
from src.utils.formatters import format_for_telegram

logger = get_logger(__name__)


class TelegramPoster:
    """Telegram poster class for publishing content to channels."""
    
    def __init__(self):
        """Initialize the Telegram poster with bot configuration."""
        try:
            self.bot = Bot(token=config.telegram_bot_token)
            self.channel_id = config.telegram_channel_id
            logger.info("Telegram poster initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Telegram poster: {str(e)}")
            raise
    
    async def post_content(self, content_data: Dict[str, Any]) -> bool:
        """
        Post content to the configured Telegram channel.
        
        Args:
            content_data: Dictionary containing the content to post
            
        Returns:
            True if posting was successful, False otherwise
        """
        try:
            logger.info("Preparing content for Telegram posting...")
            
            # Format content for Telegram
            formatted_content = format_for_telegram(content_data)
            
            # Validate content length
            if not formatted_content or len(formatted_content.strip()) == 0:
                logger.error("Content is empty after formatting")
                return False
            
            # Post to Telegram with retry logic
            success = await self._post_with_retry(formatted_content)
            
            if success:
                logger.info("Content posted to Telegram successfully")
            else:
                logger.error("Failed to post content to Telegram")
            
            return success
            
        except Exception as e:
            logger.error(f"Error posting content to Telegram: {str(e)}")
            return False
    
    async def _post_with_retry(self, content: str) -> bool:
        """
        Post content with retry logic for handling rate limits and network issues.
        
        Args:
            content: Formatted content to post
            
        Returns:
            True if posting was successful, False otherwise
        """
        max_retries = config.max_retries
        retry_delay = config.retry_delay
        
        for attempt in range(max_retries):
            try:
                logger.debug(f"Posting attempt {attempt + 1}/{max_retries}")
                
                # Send message to channel
                message = await self.bot.send_message(
                    chat_id=self.channel_id,
                    text=content,
                    parse_mode='HTML',
                    disable_web_page_preview=False
                )
                
                if message:
                    logger.info(f"Message posted successfully. Message ID: {message.message_id}")
                    return True
                
            except RetryAfter as e:
                # Handle rate limiting
                wait_time = e.retry_after
                logger.warning(f"Rate limited. Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)
                
            except TimedOut as e:
                logger.warning(f"Request timed out on attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                
            except TelegramError as e:
                logger.error(f"Telegram API error on attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                
            except Exception as e:
                logger.error(f"Unexpected error on attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
        
        logger.error("All posting attempts failed")
        return False
    
    async def test_connection(self) -> bool:
        """
        Test the connection to Telegram API and channel access.
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            logger.info("Testing Telegram connection...")
            
            # Get bot information
            bot_info = await self.bot.get_me()
            logger.info(f"Bot connected: @{bot_info.username}")
            
            # Test channel access by getting chat info
            chat_info = await self.bot.get_chat(self.channel_id)
            logger.info(f"Channel access confirmed: {chat_info.title}")
            
            return True
            
        except TelegramError as e:
            logger.error(f"Telegram connection test failed: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during connection test: {str(e)}")
            return False
    
    def post_content_sync(self, content_data: Dict[str, Any]) -> bool:
        """
        Synchronous wrapper for posting content.
        
        Args:
            content_data: Dictionary containing the content to post
            
        Returns:
            True if posting was successful, False otherwise
        """
        try:
            # Run the async post_content method
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.post_content(content_data))
            loop.close()
            return result
        except Exception as e:
            logger.error(f"Error in synchronous posting: {str(e)}")
            return False
    
    def test_connection_sync(self) -> bool:
        """
        Synchronous wrapper for testing connection.
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Run the async test_connection method
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.test_connection())
            loop.close()
            return result
        except Exception as e:
            logger.error(f"Error in synchronous connection test: {str(e)}")
            return False
