["src/tests/test_content_generator.py::TestContentGenerator::test_clean_json_response", "src/tests/test_content_generator.py::TestContentGenerator::test_create_fallback_content", "src/tests/test_content_generator.py::TestContentGenerator::test_enhance_prompt", "src/tests/test_content_generator.py::TestContentGenerator::test_parse_response_invalid_json", "src/tests/test_content_generator.py::TestContentGenerator::test_parse_response_valid_json", "src/tests/test_content_generator.py::TestContentGenerator::test_validate_content_structure", "src/tests/test_formatters.py::TestTelegramFormatter::test_build_message_complete", "src/tests/test_formatters.py::TestTelegramFormatter::test_clean_html_formatting", "src/tests/test_formatters.py::TestTelegramFormatter::test_format_content_with_dict", "src/tests/test_formatters.py::TestTelegramFormatter::test_format_content_with_json_string", "src/tests/test_formatters.py::TestTelegramFormatter::test_format_for_telegram_function", "src/tests/test_formatters.py::TestTelegramFormatter::test_parse_json_content_invalid", "src/tests/test_formatters.py::TestTelegramFormatter::test_parse_json_content_valid", "src/tests/test_formatters.py::TestTelegramFormatter::test_parse_json_content_with_markdown", "src/tests/test_formatters.py::TestTelegramFormatter::test_truncate_if_needed_long_message", "src/tests/test_formatters.py::TestTelegramFormatter::test_truncate_if_needed_short_message"]