"""
Content generation module using Google's Gemini AI API.
"""

import json
import time
from typing import Dict, Any, Optional
import google.generativeai as genai
from src.utils.config import config
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ContentGenerator:
    """Content generator class using Gemini AI API."""
    
    def __init__(self):
        """Initialize the content generator with Gemini API configuration."""
        try:
            genai.configure(api_key=config.gemini_api_key)
            self.model = genai.GenerativeModel(config.gemini_model)
            logger.info(f"Content generator initialized with model: {config.gemini_model}")
        except Exception as e:
            logger.error(f"Failed to initialize content generator: {str(e)}")
            raise
    
    def generate_content(self, prompt: str, content_type: str) -> Dict[str, Any]:
        """
        Generate content using Gemini AI based on the provided prompt.
        
        Args:
            prompt: The prompt for content generation
            content_type: Type of content being generated (e.g., 'cosmic_fact', 'myth_buster')
            
        Returns:
            Generated content as a dictionary
        """
        logger.info(f"Generating {content_type} content...")
        
        try:
            # Enhance prompt with specific instructions for JSON response
            enhanced_prompt = self._enhance_prompt(prompt, content_type)
            
            # Generate content with retries
            response = self._generate_with_retry(enhanced_prompt)
            
            # Parse and validate response
            content_data = self._parse_response(response, content_type)
            
            logger.info(f"Successfully generated {content_type} content")
            return content_data
            
        except Exception as e:
            logger.error(f"Error generating {content_type} content: {str(e)}")
            raise
    
    def _enhance_prompt(self, base_prompt: str, content_type: str) -> str:
        """
        Enhance the base prompt with specific instructions for JSON response.
        
        Args:
            base_prompt: The base prompt for content generation
            content_type: Type of content being generated
            
        Returns:
            Enhanced prompt with JSON formatting instructions
        """
        json_instructions = """
IMPORTANT INSTRUCTIONS:
1. Respond ONLY with valid JSON format
2. Use ONLY simple HTML tags for formatting: <b>, <i>, <u>, <code> (NO <html>, <body>, <div>, <p> tags)
3. Do NOT include any meta-text before or after the JSON
4. Keep content engaging and suitable for Telegram
5. Ensure content is factually accurate and educational
6. Include relevant hashtags for social media engagement
7. Do NOT use HTML document structure tags

JSON Structure Required:
{
    "title": "Engaging title for the content",
    "content": "Main content with simple HTML formatting only",
    "hashtags": ["#space", "#astronomy", "#cosmicfacts"]
}
"""
        
        enhanced_prompt = f"{json_instructions}\n\nContent Type: {content_type}\n\nPrompt: {base_prompt}"
        return enhanced_prompt
    
    def _generate_with_retry(self, prompt: str) -> str:
        """
        Generate content with retry logic for API failures.
        
        Args:
            prompt: The prompt for content generation
            
        Returns:
            Generated content response
        """
        max_retries = config.max_retries
        retry_delay = config.retry_delay
        
        for attempt in range(max_retries):
            try:
                logger.debug(f"Generation attempt {attempt + 1}/{max_retries}")
                
                response = self.model.generate_content(prompt)
                
                if response.text:
                    return response.text
                else:
                    raise ValueError("Empty response from Gemini API")
                    
            except Exception as e:
                logger.warning(f"Generation attempt {attempt + 1} failed: {str(e)}")
                
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error("All generation attempts failed")
                    raise
    
    def _parse_response(self, response: str, content_type: str) -> Dict[str, Any]:
        """
        Parse and validate the AI response.
        
        Args:
            response: Raw response from Gemini API
            content_type: Type of content for validation
            
        Returns:
            Parsed and validated content dictionary
        """
        try:
            # Clean the response to extract JSON
            cleaned_response = self._clean_json_response(response)
            
            # Parse JSON
            content_data = json.loads(cleaned_response)
            
            # Validate required fields
            self._validate_content_structure(content_data, content_type)
            
            return content_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {str(e)}")
            logger.debug(f"Raw response: {response}")
            
            # Return fallback content structure
            return self._create_fallback_content(response, content_type)
        
        except Exception as e:
            logger.error(f"Error parsing response: {str(e)}")
            raise
    
    def _clean_json_response(self, response: str) -> str:
        """
        Clean the response to extract valid JSON.
        
        Args:
            response: Raw response from API
            
        Returns:
            Cleaned JSON string
        """
        # Remove potential markdown code blocks
        response = response.strip()
        
        # Find JSON content between braces
        start_idx = response.find('{')
        end_idx = response.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return response[start_idx:end_idx + 1]
        
        # If no valid JSON structure found, return original
        return response
    
    def _validate_content_structure(self, content_data: Dict[str, Any], content_type: str) -> None:
        """
        Validate that the content has required structure.
        
        Args:
            content_data: Parsed content dictionary
            content_type: Type of content for validation
        """
        required_fields = ["title", "content"]
        
        for field in required_fields:
            if field not in content_data or not content_data[field]:
                logger.warning(f"Missing or empty required field: {field}")
    
    def _create_fallback_content(self, response: str, content_type: str) -> Dict[str, Any]:
        """
        Create fallback content structure when JSON parsing fails.
        
        Args:
            response: Raw response text
            content_type: Type of content
            
        Returns:
            Fallback content dictionary
        """
        logger.info("Creating fallback content structure")
        
        return {
            "title": f"Cosmic {content_type.replace('_', ' ').title()}",
            "content": response[:500] + "..." if len(response) > 500 else response,
            "hashtags": ["#space", "#astronomy", "#cosmicfacts"]
        }
