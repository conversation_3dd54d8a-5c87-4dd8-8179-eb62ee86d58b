"""
Topic management system for CosmicFacts content generation.
Manages CSV datasets with thousands of topics and prevents duplicates.
"""

import csv
import json
import random
from pathlib import Path
from typing import List, Dict, Set, Optional
from datetime import datetime, timedelta
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TopicManager:
    """Manages topic selection and tracking to prevent duplicates."""
    
    def __init__(self, topics_dir: str = "src/data/topics", used_topics_file: str = "src/data/used_topics.json"):
        """
        Initialize the topic manager.
        
        Args:
            topics_dir: Directory containing CSV topic files
            used_topics_file: File to track used topics
        """
        self.topics_dir = Path(topics_dir)
        self.used_topics_file = Path(used_topics_file)
        self.used_topics = self._load_used_topics()
        
        # Ensure directories exist
        self.topics_dir.mkdir(parents=True, exist_ok=True)
        self.used_topics_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create CSV files if they don't exist
        self._create_topic_datasets()
        
        logger.info("Topic manager initialized")
    
    def get_topic(self, content_type: str, force_new: bool = False) -> Optional[str]:
        """
        Get a topic for the specified content type.
        
        Args:
            content_type: Type of content (cosmic_fact, myth_buster, etc.)
            force_new: If True, force selection of unused topic
            
        Returns:
            Selected topic string or None if no topics available
        """
        try:
            # Load topics for the content type
            topics = self._load_topics(content_type)
            
            if not topics:
                logger.warning(f"No topics found for content type: {content_type}")
                return None
            
            # Get unused topics
            used_set = set(self.used_topics.get(content_type, []))
            unused_topics = [topic for topic in topics if topic not in used_set]
            
            # If no unused topics and force_new is True, reset used topics
            if not unused_topics and force_new:
                logger.info(f"All topics used for {content_type}, resetting...")
                self.used_topics[content_type] = []
                unused_topics = topics
            
            # Select topic
            if unused_topics:
                selected_topic = random.choice(unused_topics)
            else:
                # If no unused topics, select from all (allowing repeats)
                selected_topic = random.choice(topics)
                logger.warning(f"No unused topics for {content_type}, allowing repeat")
            
            # Mark topic as used
            self._mark_topic_used(content_type, selected_topic)
            
            logger.info(f"Selected topic for {content_type}: {selected_topic}")
            return selected_topic
            
        except Exception as e:
            logger.error(f"Error getting topic for {content_type}: {str(e)}")
            return None
    
    def add_topics(self, content_type: str, new_topics: List[str]) -> bool:
        """
        Add new topics to a content type's CSV file.
        
        Args:
            content_type: Type of content
            new_topics: List of new topics to add
            
        Returns:
            True if successful, False otherwise
        """
        try:
            csv_file = self.topics_dir / f"{content_type}.csv"
            
            # Load existing topics to avoid duplicates
            existing_topics = set(self._load_topics(content_type))
            
            # Filter out duplicates
            unique_new_topics = [topic for topic in new_topics if topic not in existing_topics]
            
            if not unique_new_topics:
                logger.info(f"No new unique topics to add for {content_type}")
                return True
            
            # Append new topics to CSV
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for topic in unique_new_topics:
                    writer.writerow([topic])
            
            logger.info(f"Added {len(unique_new_topics)} new topics to {content_type}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding topics to {content_type}: {str(e)}")
            return False
    
    def get_topic_stats(self) -> Dict[str, Dict[str, int]]:
        """
        Get statistics about topics for each content type.
        
        Returns:
            Dictionary with topic statistics
        """
        stats = {}
        
        for content_type in ['cosmic_fact', 'myth_buster', 'explainer', 'space_history', 
                           'cosmic_mystery', 'what_if_scenario', 'audience_question']:
            topics = self._load_topics(content_type)
            used = len(self.used_topics.get(content_type, []))
            
            stats[content_type] = {
                'total': len(topics),
                'used': used,
                'remaining': len(topics) - used
            }
        
        return stats
    
    def reset_used_topics(self, content_type: str = None) -> bool:
        """
        Reset used topics for a specific content type or all types.
        
        Args:
            content_type: Specific content type to reset, or None for all
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if content_type:
                self.used_topics[content_type] = []
                logger.info(f"Reset used topics for {content_type}")
            else:
                self.used_topics = {}
                logger.info("Reset all used topics")
            
            self._save_used_topics()
            return True
            
        except Exception as e:
            logger.error(f"Error resetting used topics: {str(e)}")
            return False
    
    def _load_topics(self, content_type: str) -> List[str]:
        """Load topics from CSV file for the specified content type."""
        csv_file = self.topics_dir / f"{content_type}.csv"
        
        if not csv_file.exists():
            logger.warning(f"Topic file not found: {csv_file}")
            return []
        
        topics = []
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader, None)  # Skip header if present
                for row in reader:
                    if row and row[0].strip():  # Skip empty rows
                        topics.append(row[0].strip())
        except Exception as e:
            logger.error(f"Error loading topics from {csv_file}: {str(e)}")
        
        return topics
    
    def _load_used_topics(self) -> Dict[str, List[str]]:
        """Load used topics from JSON file."""
        if not self.used_topics_file.exists():
            return {}
        
        try:
            with open(self.used_topics_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading used topics: {str(e)}")
            return {}
    
    def _save_used_topics(self) -> None:
        """Save used topics to JSON file."""
        try:
            with open(self.used_topics_file, 'w', encoding='utf-8') as f:
                json.dump(self.used_topics, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving used topics: {str(e)}")
    
    def _mark_topic_used(self, content_type: str, topic: str) -> None:
        """Mark a topic as used for the specified content type."""
        if content_type not in self.used_topics:
            self.used_topics[content_type] = []
        
        if topic not in self.used_topics[content_type]:
            self.used_topics[content_type].append(topic)
            self._save_used_topics()
    
    def _create_topic_datasets(self) -> None:
        """Create CSV topic datasets if they don't exist."""
        content_types = {
            'cosmic_fact': self._get_cosmic_fact_topics(),
            'myth_buster': self._get_myth_buster_topics(),
            'explainer': self._get_explainer_topics(),
            'space_history': self._get_space_history_topics(),
            'cosmic_mystery': self._get_cosmic_mystery_topics(),
            'what_if_scenario': self._get_what_if_scenario_topics(),
            'audience_question': self._get_audience_question_topics()
        }
        
        for content_type, topics in content_types.items():
            csv_file = self.topics_dir / f"{content_type}.csv"
            
            if not csv_file.exists():
                self._create_csv_file(csv_file, topics)
                logger.info(f"Created topic dataset: {csv_file}")
    
    def _create_csv_file(self, csv_file: Path, topics: List[str]) -> None:
        """Create a CSV file with the given topics."""
        try:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['topic'])  # Header
                for topic in topics:
                    writer.writerow([topic])
        except Exception as e:
            logger.error(f"Error creating CSV file {csv_file}: {str(e)}")

    def _get_cosmic_fact_topics(self) -> List[str]:
        """Generate comprehensive list of cosmic fact topics."""
        return [
            # Black Holes
            "Supermassive black holes at galaxy centers",
            "Hawking radiation and black hole evaporation",
            "Event horizon and time dilation effects",
            "Stellar mass black holes formation",
            "Intermediate mass black holes mystery",
            "Black hole information paradox",
            "Sagittarius A* our galaxy's black hole",
            "Black hole jets and accretion disks",
            "Primordial black holes from Big Bang",
            "Black hole mergers and gravitational waves",

            # Stars and Stellar Evolution
            "Red giant stars expansion and death",
            "White dwarf stars density and composition",
            "Neutron stars incredible density",
            "Pulsar stars lighthouse beacons",
            "Brown dwarf failed stars",
            "Wolf-Rayet stars massive and hot",
            "Variable stars brightness changes",
            "Binary star systems orbital dance",
            "Supernova explosions stellar death",
            "Hypernova most powerful explosions",
            "Star formation in nebulae",
            "Main sequence star lifetimes",
            "Blue supergiant stars short lives",
            "Red dwarf stars longevity",
            "Magnetar strongest magnetic fields",

            # Galaxies and Large Scale Structure
            "Milky Way galaxy structure and size",
            "Andromeda galaxy collision course",
            "Spiral galaxy arm formation",
            "Elliptical galaxy smooth appearance",
            "Dwarf galaxies satellite companions",
            "Galaxy clusters gravitational binding",
            "Dark matter galaxy scaffolding",
            "Galaxy formation early universe",
            "Active galactic nuclei power",
            "Quasars distant bright beacons",
            "Galaxy mergers cosmic collisions",
            "Local Group galaxy neighborhood",
            "Virgo Cluster nearby galaxy cluster",
            "Great Attractor mysterious pull",
            "Cosmic web large scale structure",

            # Exoplanets and Planetary Science
            "Hot Jupiter close-in gas giants",
            "Super-Earth larger rocky planets",
            "Rogue planets wandering space",
            "Tidally locked planet extremes",
            "Diamond rain on Neptune",
            "Kepler-452b Earth's cousin",
            "TRAPPIST-1 seven Earth-sized worlds",
            "Proxima Centauri b nearest exoplanet",
            "Planet formation disk evolution",
            "Habitable zone Goldilocks region",
            "Atmospheric escape planet evolution",
            "Transit method planet detection",
            "Radial velocity planet hunting",
            "Direct imaging exoplanet photography",
            "Biosignatures signs of life",

            # Solar System Wonders
            "Jupiter's Great Red Spot storm",
            "Saturn's rings composition and structure",
            "Europa's subsurface ocean",
            "Enceladus water geysers",
            "Titan's methane lakes",
            "Mars polar ice caps",
            "Venus runaway greenhouse effect",
            "Mercury's extreme temperature swings",
            "Asteroid belt rocky debris",
            "Kuiper Belt icy objects",
            "Oort Cloud comet reservoir",
            "Pluto's heart-shaped feature",
            "Ceres dwarf planet water",
            "Vesta asteroid protoplanet",
            "Halley's Comet orbital period",

            # Cosmic Phenomena
            "Gamma-ray bursts most energetic events",
            "Fast radio bursts mysterious signals",
            "Cosmic rays high-energy particles",
            "Solar wind charged particle stream",
            "Aurora borealis magnetic light show",
            "Coronal mass ejections solar storms",
            "Sunspot cycles magnetic activity",
            "Solar flares explosive energy release",
            "Interstellar medium space between stars",
            "Cosmic dust grain composition",
            "Molecular clouds star nurseries",
            "Planetary nebulae stellar death beauty",
            "Supernova remnants expanding shockwaves",
            "Herbig-Haro objects stellar jets",
            "Bok globules dark cloud cores",

            # Cosmology and Universe
            "Big Bang theory universe origin",
            "Cosmic microwave background radiation",
            "Dark energy accelerating expansion",
            "Dark matter invisible mass",
            "Inflation rapid early expansion",
            "Multiverse theory parallel universes",
            "Observable universe size limit",
            "Age of universe 13.8 billion years",
            "Hubble constant expansion rate",
            "Critical density universe fate",
            "Big Rip ultimate expansion",
            "Heat death universe end",
            "Cosmic horizon information limit",
            "Redshift distance measurement",
            "Standard candles brightness markers",

            # Space Exploration
            "Voyager probes interstellar journey",
            "Hubble Space Telescope discoveries",
            "James Webb Space Telescope infrared",
            "International Space Station orbit",
            "Mars rovers robotic exploration",
            "Cassini Saturn mission findings",
            "New Horizons Pluto flyby",
            "Parker Solar Probe sun approach",
            "Breakthrough Starshot interstellar probe",
            "SETI search for extraterrestrial intelligence",
            "Arecibo message interstellar broadcast",
            "Golden Record Voyager message",
            "Apollo moon landing achievements",
            "Space Shuttle program legacy",
            "Commercial spaceflight development",

            # Extreme Environments
            "Vacuum of space properties",
            "Absolute zero temperature limit",
            "Radiation in space dangers",
            "Microgravity effects on matter",
            "Plasma fourth state of matter",
            "Degenerate matter extreme density",
            "Exotic matter theoretical states",
            "Antimatter opposite charge particles",
            "Strange matter quark combinations",
            "Bose-Einstein condensate quantum state",
            "Superfluidity zero viscosity",
            "Superconductivity zero resistance",
            "Quantum tunneling barrier penetration",
            "Casimir effect vacuum energy",
            "Hawking radiation black hole emission",

            # Time and Space
            "Spacetime fabric Einstein's theory",
            "Gravitational time dilation effects",
            "Special relativity speed limits",
            "General relativity curved spacetime",
            "Wormholes theoretical shortcuts",
            "Time travel paradoxes",
            "Causality cause and effect",
            "Light speed universal constant",
            "Redshift cosmological expansion",
            "Parallax distance measurement",
            "Astronomical unit Earth-Sun distance",
            "Light-year distance unit",
            "Parsec stellar distance measure",
            "Cosmic calendar universe timeline",
            "Planck time smallest interval"
        ]

    def _get_myth_buster_topics(self) -> List[str]:
        """Generate comprehensive list of space myth topics to debunk."""
        return [
            # Moon and Earth Myths
            "Moon landing was faked by Hollywood",
            "Great Wall of China visible from space",
            "Moon affects human behavior and crime rates",
            "Moon is made of cheese",
            "Dark side of moon is always dark",
            "Moon causes earthquakes and disasters",
            "Tidal forces only affect oceans",
            "Earth is flat not spherical",
            "Seasons caused by Earth's distance from Sun",
            "Coriolis effect determines toilet water direction",
            "Meteors are hot when they hit ground",
            "Lightning never strikes same place twice",
            "Polaris has always been the North Star",
            "Earth's magnetic field is perfectly stable",
            "Gravity doesn't exist in space",

            # Solar System Misconceptions
            "Sun is yellow in color",
            "Mercury is hottest planet because closest to Sun",
            "Venus rotates in same direction as Earth",
            "Mars is red because it's hot",
            "Jupiter is a failed star",
            "Saturn's rings are solid structures",
            "Uranus rotates like other planets",
            "Neptune is always farthest planet",
            "Pluto was reclassified arbitrarily",
            "Asteroid belt is densely packed with rocks",
            "Comets only come from Kuiper Belt",
            "Solar wind is actual wind",
            "Sunspots are holes in the Sun",
            "Solar eclipses are extremely rare",
            "Planets align in perfect straight lines",

            # Space Travel Myths
            "Rockets need air to push against",
            "Space is completely empty vacuum",
            "Astronauts float because no gravity",
            "Space suits would explode in vacuum",
            "Blood boils instantly in space",
            "You'd freeze immediately in space",
            "Sound travels through space",
            "Spacecraft need constant thrust to move",
            "Space travel requires enormous speeds",
            "Warp drive is theoretically impossible",
            "Time travel violates physics laws",
            "Faster than light travel is impossible",
            "Space elevators are science fiction",
            "Ion drives are too weak for spacecraft",
            "Nuclear rockets are too dangerous",

            # Star and Galaxy Myths
            "Stars are all the same size",
            "Stars twinkle because they're far away",
            "North Star is brightest star",
            "Constellations look same from everywhere",
            "Stars are evenly distributed in space",
            "All stars eventually become black holes",
            "Supernovas destroy entire galaxies",
            "Neutron stars are made of neutrons only",
            "Pulsars are alien communication beacons",
            "Binary stars always orbit each other",
            "Milky Way is largest galaxy",
            "Galaxies are moving away from Earth specifically",
            "Universe expansion means galaxies stretch",
            "Dark matter is just undiscovered planets",
            "Black holes suck everything in",

            # Alien and UFO Myths
            "UFO sightings prove alien visitation",
            "Area 51 contains crashed alien spacecraft",
            "Crop circles are made by aliens",
            "Ancient aliens built pyramids",
            "SETI has detected alien signals",
            "Aliens would look like humans",
            "Faster than light travel proves alien visits",
            "Government covers up alien contact",
            "Roswell incident was alien crash",
            "Aliens abduct humans for experiments",
            "Mars face is artificial structure",
            "Wow signal was from aliens",
            "Fermi Paradox proves we're alone",
            "Aliens would use radio communication",
            "Drake Equation predicts alien contact",

            # Physics and Science Myths
            "Einstein's theories are just theories",
            "Quantum mechanics allows anything",
            "Schrödinger's cat proves parallel universes",
            "Heisenberg uncertainty principle means randomness",
            "Big Bang was an explosion in space",
            "Universe has a center and edge",
            "Time is absolute and universal",
            "Space and time are separate things",
            "Gravity is a force like magnetism",
            "Mass and weight are the same thing",
            "Energy can be created and destroyed",
            "Perpetual motion machines are possible",
            "Absolute zero stops all motion",
            "Light always travels at same speed",
            "Photons have mass like particles",

            # Telescope and Observation Myths
            "Hubble telescope orbits beyond atmosphere only",
            "Space telescopes see in all wavelengths",
            "Bigger telescopes always see farther",
            "Telescopes magnify like microscopes",
            "Radio telescopes listen for alien signals",
            "X-ray telescopes use X-ray vision",
            "Infrared telescopes see heat signatures",
            "Optical telescopes show true colors",
            "Space images are always real photographs",
            "Telescope resolution depends only on size",
            "Ground telescopes are obsolete now",
            "Amateur telescopes can't make discoveries",
            "Light pollution only affects city viewing",
            "Atmospheric turbulence always blurs images",
            "Adaptive optics eliminate all distortion",

            # Cosmic Phenomena Myths
            "Gamma-ray bursts could sterilize Earth",
            "Solar flares will destroy technology",
            "Magnetic pole reversal causes disasters",
            "Coronal mass ejections are explosions",
            "Cosmic rays are dangerous radiation",
            "Supernovas create new solar systems",
            "Quasars are nearby bright objects",
            "Fast radio bursts are alien signals",
            "Gravitational waves ripple through space",
            "Dark energy is antigravity force",
            "Wormholes connect distant regions",
            "Parallel universes exist alongside ours",
            "Time dilation only affects clocks",
            "Redshift proves universe expansion only",
            "Cosmic microwave background is heat",

            # Space Weather Myths
            "Solar storms affect human health",
            "Geomagnetic storms cause earthquakes",
            "Aurora only occur at polar regions",
            "Solar maximum happens every 11 years exactly",
            "Sunspot cycles affect Earth's climate significantly",
            "Solar wind is hot gas",
            "Van Allen belts trap all radiation",
            "Magnetosphere is perfectly spherical",
            "Space weather is like Earth weather",
            "Solar activity follows predictable patterns",
            "Cosmic rays increase during solar minimum",
            "Solar flares travel at light speed",
            "Coronal holes are actual holes",
            "Solar tsunamis are like ocean waves",
            "Space weather affects all satellites equally"
        ]
