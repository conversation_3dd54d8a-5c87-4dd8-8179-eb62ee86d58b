"""
Topic management system for CosmicFacts content generation.
Manages CSV datasets with thousands of topics and prevents duplicates.
"""

import csv
import json
import random
from pathlib import Path
from typing import List, Dict, Set, Optional
from datetime import datetime, timedelta
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TopicManager:
    """Manages topic selection and tracking to prevent duplicates."""
    
    def __init__(self, topics_dir: str = "src/data/topics", used_topics_file: str = "src/data/used_topics.json"):
        """
        Initialize the topic manager.
        
        Args:
            topics_dir: Directory containing CSV topic files
            used_topics_file: File to track used topics
        """
        self.topics_dir = Path(topics_dir)
        self.used_topics_file = Path(used_topics_file)
        self.used_topics = self._load_used_topics()
        
        # Ensure directories exist
        self.topics_dir.mkdir(parents=True, exist_ok=True)
        self.used_topics_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create CSV files if they don't exist
        self._create_topic_datasets()
        
        logger.info("Topic manager initialized")
    
    def get_topic(self, content_type: str, force_new: bool = False) -> Optional[str]:
        """
        Get a topic for the specified content type.
        
        Args:
            content_type: Type of content (cosmic_fact, myth_buster, etc.)
            force_new: If True, force selection of unused topic
            
        Returns:
            Selected topic string or None if no topics available
        """
        try:
            # Load topics for the content type
            topics = self._load_topics(content_type)
            
            if not topics:
                logger.warning(f"No topics found for content type: {content_type}")
                return None
            
            # Get unused topics
            used_set = set(self.used_topics.get(content_type, []))
            unused_topics = [topic for topic in topics if topic not in used_set]
            
            # If no unused topics and force_new is True, reset used topics
            if not unused_topics and force_new:
                logger.info(f"All topics used for {content_type}, resetting...")
                self.used_topics[content_type] = []
                unused_topics = topics
            
            # Select topic
            if unused_topics:
                selected_topic = random.choice(unused_topics)
            else:
                # If no unused topics, select from all (allowing repeats)
                selected_topic = random.choice(topics)
                logger.warning(f"No unused topics for {content_type}, allowing repeat")
            
            # Mark topic as used
            self._mark_topic_used(content_type, selected_topic)
            
            logger.info(f"Selected topic for {content_type}: {selected_topic}")
            return selected_topic
            
        except Exception as e:
            logger.error(f"Error getting topic for {content_type}: {str(e)}")
            return None
    
    def add_topics(self, content_type: str, new_topics: List[str]) -> bool:
        """
        Add new topics to a content type's CSV file.
        
        Args:
            content_type: Type of content
            new_topics: List of new topics to add
            
        Returns:
            True if successful, False otherwise
        """
        try:
            csv_file = self.topics_dir / f"{content_type}.csv"
            
            # Load existing topics to avoid duplicates
            existing_topics = set(self._load_topics(content_type))
            
            # Filter out duplicates
            unique_new_topics = [topic for topic in new_topics if topic not in existing_topics]
            
            if not unique_new_topics:
                logger.info(f"No new unique topics to add for {content_type}")
                return True
            
            # Append new topics to CSV
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for topic in unique_new_topics:
                    writer.writerow([topic])
            
            logger.info(f"Added {len(unique_new_topics)} new topics to {content_type}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding topics to {content_type}: {str(e)}")
            return False
    
    def get_topic_stats(self) -> Dict[str, Dict[str, int]]:
        """
        Get statistics about topics for each content type.
        
        Returns:
            Dictionary with topic statistics
        """
        stats = {}
        
        for content_type in ['cosmic_fact', 'myth_buster', 'explainer', 'space_history', 
                           'cosmic_mystery', 'what_if_scenario', 'audience_question']:
            topics = self._load_topics(content_type)
            used = len(self.used_topics.get(content_type, []))
            
            stats[content_type] = {
                'total': len(topics),
                'used': used,
                'remaining': len(topics) - used
            }
        
        return stats
    
    def reset_used_topics(self, content_type: str = None) -> bool:
        """
        Reset used topics for a specific content type or all types.
        
        Args:
            content_type: Specific content type to reset, or None for all
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if content_type:
                self.used_topics[content_type] = []
                logger.info(f"Reset used topics for {content_type}")
            else:
                self.used_topics = {}
                logger.info("Reset all used topics")
            
            self._save_used_topics()
            return True
            
        except Exception as e:
            logger.error(f"Error resetting used topics: {str(e)}")
            return False
    
    def _load_topics(self, content_type: str) -> List[str]:
        """Load topics from CSV file for the specified content type."""
        csv_file = self.topics_dir / f"{content_type}.csv"
        
        if not csv_file.exists():
            logger.warning(f"Topic file not found: {csv_file}")
            return []
        
        topics = []
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader, None)  # Skip header if present
                for row in reader:
                    if row and row[0].strip():  # Skip empty rows
                        topics.append(row[0].strip())
        except Exception as e:
            logger.error(f"Error loading topics from {csv_file}: {str(e)}")
        
        return topics
    
    def _load_used_topics(self) -> Dict[str, List[str]]:
        """Load used topics from JSON file."""
        if not self.used_topics_file.exists():
            return {}
        
        try:
            with open(self.used_topics_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading used topics: {str(e)}")
            return {}
    
    def _save_used_topics(self) -> None:
        """Save used topics to JSON file."""
        try:
            with open(self.used_topics_file, 'w', encoding='utf-8') as f:
                json.dump(self.used_topics, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Error saving used topics: {str(e)}")
    
    def _mark_topic_used(self, content_type: str, topic: str) -> None:
        """Mark a topic as used for the specified content type."""
        if content_type not in self.used_topics:
            self.used_topics[content_type] = []
        
        if topic not in self.used_topics[content_type]:
            self.used_topics[content_type].append(topic)
            self._save_used_topics()
    
    def _create_topic_datasets(self) -> None:
        """Create CSV topic datasets if they don't exist."""
        content_types = {
            'cosmic_fact': self._get_cosmic_fact_topics(),
            'myth_buster': self._get_myth_buster_topics(),
            'explainer': self._get_explainer_topics(),
            'space_history': self._get_space_history_topics(),
            'cosmic_mystery': self._get_cosmic_mystery_topics(),
            'what_if_scenario': self._get_what_if_scenario_topics(),
            'audience_question': self._get_audience_question_topics()
        }
        
        for content_type, topics in content_types.items():
            csv_file = self.topics_dir / f"{content_type}.csv"
            
            if not csv_file.exists():
                self._create_csv_file(csv_file, topics)
                logger.info(f"Created topic dataset: {csv_file}")
    
    def _create_csv_file(self, csv_file: Path, topics: List[str]) -> None:
        """Create a CSV file with the given topics."""
        try:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['topic'])  # Header
                for topic in topics:
                    writer.writerow([topic])
        except Exception as e:
            logger.error(f"Error creating CSV file {csv_file}: {str(e)}")

    def _get_cosmic_fact_topics(self) -> List[str]:
        """Generate comprehensive list of cosmic fact topics."""
        return [
            # Black Holes
            "Supermassive black holes at galaxy centers",
            "Hawking radiation and black hole evaporation",
            "Event horizon and time dilation effects",
            "Stellar mass black holes formation",
            "Intermediate mass black holes mystery",
            "Black hole information paradox",
            "Sagittarius A* our galaxy's black hole",
            "Black hole jets and accretion disks",
            "Primordial black holes from Big Bang",
            "Black hole mergers and gravitational waves",

            # Stars and Stellar Evolution
            "Red giant stars expansion and death",
            "White dwarf stars density and composition",
            "Neutron stars incredible density",
            "Pulsar stars lighthouse beacons",
            "Brown dwarf failed stars",
            "Wolf-Rayet stars massive and hot",
            "Variable stars brightness changes",
            "Binary star systems orbital dance",
            "Supernova explosions stellar death",
            "Hypernova most powerful explosions",
            "Star formation in nebulae",
            "Main sequence star lifetimes",
            "Blue supergiant stars short lives",
            "Red dwarf stars longevity",
            "Magnetar strongest magnetic fields",

            # Galaxies and Large Scale Structure
            "Milky Way galaxy structure and size",
            "Andromeda galaxy collision course",
            "Spiral galaxy arm formation",
            "Elliptical galaxy smooth appearance",
            "Dwarf galaxies satellite companions",
            "Galaxy clusters gravitational binding",
            "Dark matter galaxy scaffolding",
            "Galaxy formation early universe",
            "Active galactic nuclei power",
            "Quasars distant bright beacons",
            "Galaxy mergers cosmic collisions",
            "Local Group galaxy neighborhood",
            "Virgo Cluster nearby galaxy cluster",
            "Great Attractor mysterious pull",
            "Cosmic web large scale structure",

            # Exoplanets and Planetary Science
            "Hot Jupiter close-in gas giants",
            "Super-Earth larger rocky planets",
            "Rogue planets wandering space",
            "Tidally locked planet extremes",
            "Diamond rain on Neptune",
            "Kepler-452b Earth's cousin",
            "TRAPPIST-1 seven Earth-sized worlds",
            "Proxima Centauri b nearest exoplanet",
            "Planet formation disk evolution",
            "Habitable zone Goldilocks region",
            "Atmospheric escape planet evolution",
            "Transit method planet detection",
            "Radial velocity planet hunting",
            "Direct imaging exoplanet photography",
            "Biosignatures signs of life",

            # Solar System Wonders
            "Jupiter's Great Red Spot storm",
            "Saturn's rings composition and structure",
            "Europa's subsurface ocean",
            "Enceladus water geysers",
            "Titan's methane lakes",
            "Mars polar ice caps",
            "Venus runaway greenhouse effect",
            "Mercury's extreme temperature swings",
            "Asteroid belt rocky debris",
            "Kuiper Belt icy objects",
            "Oort Cloud comet reservoir",
            "Pluto's heart-shaped feature",
            "Ceres dwarf planet water",
            "Vesta asteroid protoplanet",
            "Halley's Comet orbital period",

            # Cosmic Phenomena
            "Gamma-ray bursts most energetic events",
            "Fast radio bursts mysterious signals",
            "Cosmic rays high-energy particles",
            "Solar wind charged particle stream",
            "Aurora borealis magnetic light show",
            "Coronal mass ejections solar storms",
            "Sunspot cycles magnetic activity",
            "Solar flares explosive energy release",
            "Interstellar medium space between stars",
            "Cosmic dust grain composition",
            "Molecular clouds star nurseries",
            "Planetary nebulae stellar death beauty",
            "Supernova remnants expanding shockwaves",
            "Herbig-Haro objects stellar jets",
            "Bok globules dark cloud cores",

            # Cosmology and Universe
            "Big Bang theory universe origin",
            "Cosmic microwave background radiation",
            "Dark energy accelerating expansion",
            "Dark matter invisible mass",
            "Inflation rapid early expansion",
            "Multiverse theory parallel universes",
            "Observable universe size limit",
            "Age of universe 13.8 billion years",
            "Hubble constant expansion rate",
            "Critical density universe fate",
            "Big Rip ultimate expansion",
            "Heat death universe end",
            "Cosmic horizon information limit",
            "Redshift distance measurement",
            "Standard candles brightness markers",

            # Space Exploration
            "Voyager probes interstellar journey",
            "Hubble Space Telescope discoveries",
            "James Webb Space Telescope infrared",
            "International Space Station orbit",
            "Mars rovers robotic exploration",
            "Cassini Saturn mission findings",
            "New Horizons Pluto flyby",
            "Parker Solar Probe sun approach",
            "Breakthrough Starshot interstellar probe",
            "SETI search for extraterrestrial intelligence",
            "Arecibo message interstellar broadcast",
            "Golden Record Voyager message",
            "Apollo moon landing achievements",
            "Space Shuttle program legacy",
            "Commercial spaceflight development",

            # Extreme Environments
            "Vacuum of space properties",
            "Absolute zero temperature limit",
            "Radiation in space dangers",
            "Microgravity effects on matter",
            "Plasma fourth state of matter",
            "Degenerate matter extreme density",
            "Exotic matter theoretical states",
            "Antimatter opposite charge particles",
            "Strange matter quark combinations",
            "Bose-Einstein condensate quantum state",
            "Superfluidity zero viscosity",
            "Superconductivity zero resistance",
            "Quantum tunneling barrier penetration",
            "Casimir effect vacuum energy",
            "Hawking radiation black hole emission",

            # Time and Space
            "Spacetime fabric Einstein's theory",
            "Gravitational time dilation effects",
            "Special relativity speed limits",
            "General relativity curved spacetime",
            "Wormholes theoretical shortcuts",
            "Time travel paradoxes",
            "Causality cause and effect",
            "Light speed universal constant",
            "Redshift cosmological expansion",
            "Parallax distance measurement",
            "Astronomical unit Earth-Sun distance",
            "Light-year distance unit",
            "Parsec stellar distance measure",
            "Cosmic calendar universe timeline",
            "Planck time smallest interval"
        ]

    def _get_myth_buster_topics(self) -> List[str]:
        """Generate comprehensive list of space myth topics to debunk."""
        return [
            # Moon and Earth Myths
            "Moon landing was faked by Hollywood",
            "Great Wall of China visible from space",
            "Moon affects human behavior and crime rates",
            "Moon is made of cheese",
            "Dark side of moon is always dark",
            "Moon causes earthquakes and disasters",
            "Tidal forces only affect oceans",
            "Earth is flat not spherical",
            "Seasons caused by Earth's distance from Sun",
            "Coriolis effect determines toilet water direction",
            "Meteors are hot when they hit ground",
            "Lightning never strikes same place twice",
            "Polaris has always been the North Star",
            "Earth's magnetic field is perfectly stable",
            "Gravity doesn't exist in space",

            # Solar System Misconceptions
            "Sun is yellow in color",
            "Mercury is hottest planet because closest to Sun",
            "Venus rotates in same direction as Earth",
            "Mars is red because it's hot",
            "Jupiter is a failed star",
            "Saturn's rings are solid structures",
            "Uranus rotates like other planets",
            "Neptune is always farthest planet",
            "Pluto was reclassified arbitrarily",
            "Asteroid belt is densely packed with rocks",
            "Comets only come from Kuiper Belt",
            "Solar wind is actual wind",
            "Sunspots are holes in the Sun",
            "Solar eclipses are extremely rare",
            "Planets align in perfect straight lines",

            # Space Travel Myths
            "Rockets need air to push against",
            "Space is completely empty vacuum",
            "Astronauts float because no gravity",
            "Space suits would explode in vacuum",
            "Blood boils instantly in space",
            "You'd freeze immediately in space",
            "Sound travels through space",
            "Spacecraft need constant thrust to move",
            "Space travel requires enormous speeds",
            "Warp drive is theoretically impossible",
            "Time travel violates physics laws",
            "Faster than light travel is impossible",
            "Space elevators are science fiction",
            "Ion drives are too weak for spacecraft",
            "Nuclear rockets are too dangerous",

            # Star and Galaxy Myths
            "Stars are all the same size",
            "Stars twinkle because they're far away",
            "North Star is brightest star",
            "Constellations look same from everywhere",
            "Stars are evenly distributed in space",
            "All stars eventually become black holes",
            "Supernovas destroy entire galaxies",
            "Neutron stars are made of neutrons only",
            "Pulsars are alien communication beacons",
            "Binary stars always orbit each other",
            "Milky Way is largest galaxy",
            "Galaxies are moving away from Earth specifically",
            "Universe expansion means galaxies stretch",
            "Dark matter is just undiscovered planets",
            "Black holes suck everything in",

            # Alien and UFO Myths
            "UFO sightings prove alien visitation",
            "Area 51 contains crashed alien spacecraft",
            "Crop circles are made by aliens",
            "Ancient aliens built pyramids",
            "SETI has detected alien signals",
            "Aliens would look like humans",
            "Faster than light travel proves alien visits",
            "Government covers up alien contact",
            "Roswell incident was alien crash",
            "Aliens abduct humans for experiments",
            "Mars face is artificial structure",
            "Wow signal was from aliens",
            "Fermi Paradox proves we're alone",
            "Aliens would use radio communication",
            "Drake Equation predicts alien contact",

            # Physics and Science Myths
            "Einstein's theories are just theories",
            "Quantum mechanics allows anything",
            "Schrödinger's cat proves parallel universes",
            "Heisenberg uncertainty principle means randomness",
            "Big Bang was an explosion in space",
            "Universe has a center and edge",
            "Time is absolute and universal",
            "Space and time are separate things",
            "Gravity is a force like magnetism",
            "Mass and weight are the same thing",
            "Energy can be created and destroyed",
            "Perpetual motion machines are possible",
            "Absolute zero stops all motion",
            "Light always travels at same speed",
            "Photons have mass like particles",

            # Telescope and Observation Myths
            "Hubble telescope orbits beyond atmosphere only",
            "Space telescopes see in all wavelengths",
            "Bigger telescopes always see farther",
            "Telescopes magnify like microscopes",
            "Radio telescopes listen for alien signals",
            "X-ray telescopes use X-ray vision",
            "Infrared telescopes see heat signatures",
            "Optical telescopes show true colors",
            "Space images are always real photographs",
            "Telescope resolution depends only on size",
            "Ground telescopes are obsolete now",
            "Amateur telescopes can't make discoveries",
            "Light pollution only affects city viewing",
            "Atmospheric turbulence always blurs images",
            "Adaptive optics eliminate all distortion",

            # Cosmic Phenomena Myths
            "Gamma-ray bursts could sterilize Earth",
            "Solar flares will destroy technology",
            "Magnetic pole reversal causes disasters",
            "Coronal mass ejections are explosions",
            "Cosmic rays are dangerous radiation",
            "Supernovas create new solar systems",
            "Quasars are nearby bright objects",
            "Fast radio bursts are alien signals",
            "Gravitational waves ripple through space",
            "Dark energy is antigravity force",
            "Wormholes connect distant regions",
            "Parallel universes exist alongside ours",
            "Time dilation only affects clocks",
            "Redshift proves universe expansion only",
            "Cosmic microwave background is heat",

            # Space Weather Myths
            "Solar storms affect human health",
            "Geomagnetic storms cause earthquakes",
            "Aurora only occur at polar regions",
            "Solar maximum happens every 11 years exactly",
            "Sunspot cycles affect Earth's climate significantly",
            "Solar wind is hot gas",
            "Van Allen belts trap all radiation",
            "Magnetosphere is perfectly spherical",
            "Space weather is like Earth weather",
            "Solar activity follows predictable patterns",
            "Cosmic rays increase during solar minimum",
            "Solar flares travel at light speed",
            "Coronal holes are actual holes",
            "Solar tsunamis are like ocean waves",
            "Space weather affects all satellites equally"
        ]

    def _get_explainer_topics(self) -> List[str]:
        """Generate comprehensive list of complex space concepts to explain."""
        return [
            # Fundamental Physics Concepts
            "How gravity works and warps spacetime",
            "What is dark matter and why can't we see it",
            "How stars form from gas and dust clouds",
            "What are gravitational waves and how we detect them",
            "How do rockets work in the vacuum of space",
            "What is the speed of light and why it's constant",
            "How does nuclear fusion power the Sun",
            "What causes time dilation near massive objects",
            "How do black holes bend light and time",
            "What is quantum mechanics in simple terms",
            "How does the electromagnetic spectrum work",
            "What are the four fundamental forces",
            "How does matter and antimatter interact",
            "What is entropy and the arrow of time",
            "How do magnetic fields work in space",

            # Astronomical Phenomena
            "How do solar eclipses happen",
            "What causes the phases of the Moon",
            "How do tides work on Earth",
            "What makes auroras appear in the sky",
            "How do meteor showers occur",
            "What causes seasonal changes on Earth",
            "How do planetary rings form and persist",
            "What makes some stars variable in brightness",
            "How do binary star systems work",
            "What causes stellar explosions like supernovas",
            "How do pulsars create regular radio pulses",
            "What makes quasars so incredibly bright",
            "How do galaxy collisions unfold",
            "What causes gamma-ray bursts",
            "How do cosmic rays travel through space",

            # Space Technology and Exploration
            "How do we navigate spacecraft in space",
            "What is orbital mechanics and how satellites stay up",
            "How do space telescopes see different wavelengths",
            "What is the greenhouse effect on planets",
            "How do we measure distances to stars",
            "What is redshift and how it shows expansion",
            "How do we detect exoplanets around other stars",
            "What is the Doppler effect in astronomy",
            "How do radio telescopes work",
            "What is adaptive optics in ground telescopes",
            "How do we communicate with deep space probes",
            "What is aerobraking for spacecraft",
            "How do ion drives propel spacecraft",
            "What is a gravity assist or slingshot maneuver",
            "How do we land rovers on Mars",

            # Cosmology and Universe Structure
            "What is the Big Bang theory",
            "How do we know the universe is expanding",
            "What is cosmic microwave background radiation",
            "How did the first stars and galaxies form",
            "What is dark energy and cosmic acceleration",
            "How do we measure the age of the universe",
            "What is the cosmic web structure",
            "How do galaxy clusters form and evolve",
            "What is nucleosynthesis in the early universe",
            "How do we study the most distant galaxies",
            "What is inflation in the early universe",
            "How do we map dark matter distribution",
            "What are the different types of galaxies",
            "How do supermassive black holes grow",
            "What is the multiverse theory",

            # Planetary Science
            "How do planets form around stars",
            "What makes a planet habitable",
            "How do atmospheres evolve on planets",
            "What causes extreme weather on gas giants",
            "How do moons form around planets",
            "What is tidal locking and its effects",
            "How do we study planetary interiors",
            "What causes volcanic activity on moons",
            "How do planetary magnetic fields work",
            "What is atmospheric escape from planets",
            "How do we analyze exoplanet atmospheres",
            "What causes retrograde rotation on planets",
            "How do impact craters form and persist",
            "What is differentiation in planetary formation",
            "How do we determine planetary compositions",

            # Stellar Evolution and Death
            "How do stars live and die",
            "What determines a star's lifetime",
            "How do white dwarf stars form",
            "What happens when stars run out of fuel",
            "How do neutron stars form from supernovas",
            "What makes some stars become black holes",
            "How do red giant stars expand",
            "What are the different types of supernovas",
            "How do stellar remnants cool over time",
            "What is the chandrasekhar limit",
            "How do stars create heavy elements",
            "What causes stars to pulsate",
            "How do massive stars end their lives",
            "What is stellar nucleosynthesis",
            "How do brown dwarfs differ from stars",

            # Space Environment and Conditions
            "What is the vacuum of space really like",
            "How cold is space and why",
            "What is radiation in space and its dangers",
            "How do cosmic rays affect astronauts",
            "What is the solar wind and its effects",
            "How do magnetic fields protect planets",
            "What is space weather and its impacts",
            "How do micrometeoroids pose threats",
            "What is the interstellar medium",
            "How do shock waves propagate in space",
            "What are the Van Allen radiation belts",
            "How does the heliosphere protect us",
            "What is the termination shock",
            "How do planetary magnetospheres work",
            "What is the bow shock around Earth",

            # Observational Astronomy
            "How do we see objects billions of light-years away",
            "What is parallax and stellar distance measurement",
            "How do we determine stellar temperatures",
            "What is spectroscopy and what it reveals",
            "How do we measure stellar masses",
            "What is photometry in astronomy",
            "How do we study invisible wavelengths",
            "What is interferometry in astronomy",
            "How do we correct for atmospheric distortion",
            "What is the cosmic distance ladder",
            "How do we measure galaxy rotation curves",
            "What is gravitational lensing",
            "How do we detect dark matter indirectly",
            "What is standard candle brightness measurement",
            "How do we study the early universe"
        ]

    def _get_space_history_topics(self) -> List[str]:
        """Generate comprehensive list of space history topics."""
        return [
            # Early Space Race and Pioneers
            "Sputnik 1 first artificial satellite launch",
            "Yuri Gagarin first human in space",
            "Alan Shepard first American in space",
            "Valentina Tereshkova first woman in space",
            "Alexei Leonov first spacewalk",
            "Ed White first American spacewalk",
            "Luna 2 first human-made object to reach Moon",
            "Luna 3 first photos of Moon's far side",
            "Venera 1 first Venus flyby attempt",
            "Mariner 2 first successful Venus flyby",
            "Konstantin Tsiolkovsky rocket equation pioneer",
            "Robert Goddard liquid fuel rocket inventor",
            "Hermann Oberth spaceflight theorist",
            "Sergei Korolev chief designer of Soviet space program",
            "Wernher von Braun Saturn V rocket designer",

            # Apollo Program and Moon Landing
            "Apollo 1 tragic fire and crew loss",
            "Apollo 8 first humans to orbit Moon",
            "Apollo 11 first Moon landing",
            "Neil Armstrong first steps on Moon",
            "Buzz Aldrin lunar module pilot",
            "Michael Collins command module pilot",
            "Apollo 12 precision Moon landing",
            "Apollo 13 successful failure mission",
            "Apollo 14 Alan Shepard's Moon golf shot",
            "Apollo 15 first lunar rover use",
            "Apollo 16 highlands exploration",
            "Apollo 17 last humans on Moon",
            "Gene Cernan last person to walk on Moon",
            "Harrison Schmitt only scientist on Moon",
            "Saturn V most powerful rocket ever",

            # Space Stations and Long Duration Flight
            "Salyut 1 first space station",
            "Skylab America's first space station",
            "Mir space station construction",
            "International Space Station partnership",
            "Expedition 1 first ISS crew",
            "Valeri Polyakov longest single spaceflight",
            "Scott Kelly year in space mission",
            "Peggy Whitson most experienced female astronaut",
            "Space station docking procedures development",
            "EVA spacewalk techniques evolution",
            "Life support systems advancement",
            "Microgravity research breakthroughs",
            "Commercial crew program development",
            "SpaceX Dragon first commercial crew",
            "Boeing Starliner development challenges",

            # Robotic Exploration Milestones
            "Viking 1 and 2 Mars landers",
            "Voyager 1 and 2 grand tour",
            "Pioneer 10 first Jupiter flyby",
            "Pioneer 11 first Saturn flyby",
            "Galileo Jupiter orbiter mission",
            "Cassini-Huygens Saturn exploration",
            "New Horizons Pluto flyby",
            "Mars Pathfinder and Sojourner rover",
            "Spirit and Opportunity Mars rovers",
            "Curiosity Mars Science Laboratory",
            "Perseverance Mars sample collection",
            "Ingenuity Mars helicopter flight",
            "Hubble Space Telescope deployment",
            "James Webb Space Telescope launch",
            "Kepler exoplanet hunting mission",

            # Space Shuttle Era
            "Space Shuttle Columbia first flight",
            "Space Shuttle Challenger disaster",
            "Space Shuttle Discovery most flights",
            "Space Shuttle Atlantis final mission",
            "Space Shuttle Endeavour construction",
            "Hubble Space Telescope servicing missions",
            "International Space Station construction",
            "Spacelab scientific missions",
            "Satellite deployment and retrieval",
            "Space Shuttle-Mir program",
            "First teacher in space program",
            "Space Shuttle return to flight",
            "Space Shuttle retirement decision",
            "Commercial cargo program transition",
            "Space Shuttle legacy and impact",

            # International Cooperation
            "Apollo-Soyuz Test Project handshake",
            "International Space Station partnership",
            "European Space Agency formation",
            "Japan Aerospace Exploration Agency",
            "Canadian Space Agency contributions",
            "Russian space program evolution",
            "Chinese space program development",
            "Indian space program achievements",
            "Commercial space industry growth",
            "Private space companies emergence",
            "International space law development",
            "Outer Space Treaty signing",
            "Moon Agreement negotiations",
            "Space debris mitigation efforts",
            "Planetary protection protocols",

            # Scientific Discoveries
            "Cosmic microwave background discovery",
            "Pulsars discovery by Jocelyn Bell",
            "Quasars identification and study",
            "Black hole existence confirmation",
            "Gravitational waves detection",
            "Exoplanet discoveries beginning",
            "Dark matter evidence accumulation",
            "Dark energy discovery",
            "Accelerating universe revelation",
            "Higgs boson detection",
            "Neutrino astronomy development",
            "Gamma-ray burst understanding",
            "Fast radio burst discoveries",
            "Gravitational lensing observations",
            "Cosmic ray origin studies",

            # Space Technology Breakthroughs
            "Rocket engine development history",
            "Heat shield technology advancement",
            "Life support system evolution",
            "Navigation system improvements",
            "Communication satellite deployment",
            "GPS constellation establishment",
            "Weather satellite networks",
            "Earth observation satellite programs",
            "Spy satellite development",
            "Reusable rocket technology",
            "Ion propulsion system development",
            "Solar panel efficiency improvements",
            "Radioisotope thermoelectric generators",
            "Autonomous spacecraft operations",
            "Artificial intelligence in space",

            # Disasters and Lessons Learned
            "Apollo 1 fire investigation",
            "Challenger disaster investigation",
            "Columbia disaster analysis",
            "Soyuz 1 parachute failure",
            "Soyuz 11 depressurization tragedy",
            "Mars Climate Orbiter metric mixup",
            "Mars Polar Lander communication loss",
            "Beagle 2 Mars lander silence",
            "Phobos-Grunt mission failure",
            "Schiaparelli lander crash",
            "DART asteroid impact success",
            "Space debris collision events",
            "Solar storm spacecraft impacts",
            "Launch vehicle failure investigations",
            "Safety protocol improvements"
        ]

    def _get_cosmic_mystery_topics(self) -> List[str]:
        """Generate comprehensive list of cosmic mysteries and unsolved questions."""
        return [
            # Dark Matter and Dark Energy
            "What is dark matter made of",
            "Why can't we detect dark matter directly",
            "How does dark energy accelerate expansion",
            "What is the nature of dark energy",
            "Why is there more dark matter than regular matter",
            "How do dark matter and dark energy interact",
            "What happens to dark matter in black holes",
            "Could dark matter be primordial black holes",
            "Why doesn't dark matter form structures like stars",
            "How did dark matter influence early universe",
            "What is the dark matter halo around galaxies",
            "Could modified gravity explain dark matter",
            "Why is dark energy density constant",
            "What is the cosmological constant problem",
            "How will dark energy affect universe's fate",

            # Black Hole Mysteries
            "What happens inside a black hole",
            "Do black holes destroy information",
            "What is the black hole information paradox",
            "How do black holes evaporate via Hawking radiation",
            "What is the firewall paradox",
            "Do wormholes exist inside black holes",
            "What happens at the singularity",
            "How do supermassive black holes form so quickly",
            "What is the relationship between black holes and galaxies",
            "Can anything escape from inside event horizon",
            "What is the holographic principle",
            "How do black holes merge and create gravitational waves",
            "What is the no-hair theorem",
            "Do primordial black holes exist",
            "What is Hawking's area theorem",

            # Fast Radio Bursts and Cosmic Signals
            "What causes fast radio bursts",
            "Why do some FRBs repeat while others don't",
            "What is the source of the Wow signal",
            "Are fast radio bursts from alien civilizations",
            "What creates such intense radio emissions",
            "Why are FRBs so brief and powerful",
            "What is the dispersion measure telling us",
            "Could FRBs be from magnetars",
            "What is the rate of FRB occurrence",
            "Why do FRBs come from distant galaxies",
            "What is the energy source for FRBs",
            "Could FRBs be from cosmic strings",
            "What is the polarization of FRB signals",
            "Why are FRBs mostly at specific frequencies",
            "What is the host galaxy environment of FRBs",

            # Gamma-Ray Bursts
            "What triggers the most powerful explosions",
            "How do gamma-ray bursts release so much energy",
            "What is the difference between long and short GRBs",
            "What creates the gamma-ray burst jets",
            "Why are GRBs beamed in specific directions",
            "What is the central engine of GRBs",
            "How do GRBs affect their host galaxies",
            "Could a nearby GRB threaten Earth",
            "What is the afterglow mechanism",
            "Why do some GRBs have optical counterparts",
            "What is the rate of GRB occurrence",
            "How do GRBs relate to supernovae",
            "What creates short gamma-ray bursts",
            "Why are GRBs associated with star formation",
            "What is the prompt emission mechanism",

            # Cosmic Rays and High-Energy Particles
            "Where do the highest energy cosmic rays come from",
            "What accelerates particles to such extreme energies",
            "Why is there a cosmic ray cutoff at highest energies",
            "What is the source of ultra-high-energy cosmic rays",
            "How do cosmic rays travel through magnetic fields",
            "What creates the cosmic ray knee and ankle",
            "Why do cosmic rays have specific composition",
            "What is the role of supernova remnants",
            "How do cosmic rays escape the galaxy",
            "What is the cosmic ray anisotropy",
            "Why are there cosmic ray air shower variations",
            "What accelerates cosmic rays in galaxy clusters",
            "How do cosmic rays interact with interstellar medium",
            "What is the cosmic ray spectrum shape",
            "Why do cosmic rays correlate with nearby sources",

            # Neutrino Mysteries
            "Why do neutrinos have mass",
            "What is the neutrino mass hierarchy",
            "Are neutrinos their own antiparticles",
            "What is neutrinoless double beta decay",
            "How many types of neutrinos exist",
            "What is the sterile neutrino evidence",
            "Why is there a neutrino flavor oscillation",
            "What creates high-energy astrophysical neutrinos",
            "How do neutrinos interact with dark matter",
            "What is the cosmic neutrino background",
            "Why are there neutrino anomalies in experiments",
            "What is the neutrino magnetic moment",
            "How do neutrinos affect Big Bang nucleosynthesis",
            "What creates the IceCube neutrino events",
            "Why are neutrino cross-sections so small",

            # Antimatter and Matter Asymmetry
            "Why is there more matter than antimatter",
            "What happened to primordial antimatter",
            "How did matter-antimatter asymmetry arise",
            "What is CP violation in particle physics",
            "Where does cosmic antimatter come from",
            "Why don't we see antimatter galaxies",
            "What is the baryon asymmetry problem",
            "How do we detect cosmic antimatter",
            "What creates positron excess in cosmic rays",
            "Why is antimatter so rare in universe",
            "What is the matter-antimatter annihilation rate",
            "How did baryogenesis occur in early universe",
            "What is the role of neutrinos in asymmetry",
            "Why is there charge-parity violation",
            "What creates antimatter in space",

            # Quantum Mechanics and Spacetime
            "What is the nature of quantum gravity",
            "How does quantum mechanics apply to black holes",
            "What is the measurement problem in quantum mechanics",
            "Why does quantum entanglement work instantly",
            "What is the role of consciousness in quantum mechanics",
            "How do virtual particles affect spacetime",
            "What is the quantum vacuum energy",
            "Why is there quantum uncertainty",
            "What creates quantum tunneling effects",
            "How does decoherence work in quantum systems",
            "What is the many-worlds interpretation",
            "Why does wave function collapse occur",
            "What is quantum field theory in curved spacetime",
            "How do quantum fluctuations create particles",
            "What is the holographic principle in physics",

            # Fermi Paradox and Extraterrestrial Life
            "Where is everybody in the universe",
            "Why haven't we detected alien civilizations",
            "What is the Great Filter hypothesis",
            "Are we alone in the universe",
            "Why is there no evidence of alien technology",
            "What is the Drake equation missing",
            "Could aliens be hiding from us",
            "Why don't we see Dyson spheres",
            "What is the zoo hypothesis",
            "Are we living in a simulation",
            "Why is Earth apparently special",
            "What is the rare Earth hypothesis",
            "Could aliens be too different to recognize",
            "Why is there no alien archaeology",
            "What is the transcension hypothesis"
        ]

    def _get_what_if_scenario_topics(self) -> List[str]:
        """Generate comprehensive list of what-if scenarios for space topics."""
        return [
            # Earth and Solar System Changes
            "What if Earth had rings like Saturn",
            "What if the Moon was twice as close to Earth",
            "What if Earth had two moons",
            "What if Earth's day was 48 hours long",
            "What if Earth's axis wasn't tilted",
            "What if Earth had no magnetic field",
            "What if Earth was tidally locked to the Sun",
            "What if Earth orbited a red dwarf star",
            "What if Jupiter was in Earth's orbit",
            "What if Saturn's rings surrounded Earth",
            "What if Earth had no atmosphere",
            "What if Earth was 50% larger",
            "What if Earth's gravity was twice as strong",
            "What if Earth had a retrograde rotation",
            "What if Earth was in a binary star system",

            # Solar System Modifications
            "What if the Sun was a red giant star",
            "What if the Sun suddenly disappeared",
            "What if the Sun was half its current mass",
            "What if Mars was in the habitable zone",
            "What if Venus had Earth-like conditions",
            "What if Jupiter became a star",
            "What if Saturn lost all its rings",
            "What if Uranus wasn't tilted on its side",
            "What if Neptune was the outermost planet",
            "What if Pluto was still considered a planet",
            "What if the asteroid belt formed a planet",
            "What if there was a planet between Mercury and Venus",
            "What if all planets orbited in the same direction",
            "What if the solar system had 20 planets",
            "What if comets didn't exist",

            # Stellar and Galactic Scenarios
            "What if our Sun was part of a binary system",
            "What if Betelgeuse exploded as a supernova",
            "What if a black hole entered our solar system",
            "What if the Milky Way had no central black hole",
            "What if Andromeda collided with us tomorrow",
            "What if our solar system was at galaxy's center",
            "What if the Milky Way was an elliptical galaxy",
            "What if there were no other galaxies",
            "What if dark matter didn't exist",
            "What if the universe wasn't expanding",
            "What if gravity was twice as strong everywhere",
            "What if the speed of light was much slower",
            "What if time moved backwards",
            "What if space had four dimensions",
            "What if the Big Bang never happened",

            # Technological and Exploration Scenarios
            "What if we could travel at light speed",
            "What if we discovered faster-than-light travel",
            "What if we could build a space elevator",
            "What if we terraformed Mars successfully",
            "What if we found life on Europa",
            "What if we received an alien radio signal",
            "What if aliens visited Earth openly",
            "What if we could mine asteroids easily",
            "What if we built cities on the Moon",
            "What if we could control gravity",
            "What if we mastered nuclear fusion",
            "What if we could create artificial black holes",
            "What if we developed perfect life support",
            "What if we could hibernate for centuries",
            "What if we could upload consciousness",

            # Physics and Reality Alterations
            "What if quantum mechanics worked differently",
            "What if there were no uncertainty principle",
            "What if antimatter was more common",
            "What if the fundamental constants changed",
            "What if there were five fundamental forces",
            "What if entropy could be reversed",
            "What if energy wasn't conserved",
            "What if mass and energy weren't equivalent",
            "What if spacetime wasn't curved",
            "What if parallel universes were accessible",
            "What if time travel was possible",
            "What if causality didn't exist",
            "What if consciousness affected reality",
            "What if the multiverse theory was proven",
            "What if we lived in a computer simulation",

            # Cosmic Event Scenarios
            "What if a gamma-ray burst hit Earth",
            "What if a massive asteroid hit Earth tomorrow",
            "What if the Sun's magnetic field reversed",
            "What if a rogue planet entered our system",
            "What if the Oort Cloud was disturbed",
            "What if all satellites suddenly stopped working",
            "What if the Van Allen belts disappeared",
            "What if cosmic rays increased dramatically",
            "What if the cosmic microwave background changed",
            "What if dark energy suddenly stopped",
            "What if the Higgs field became unstable",
            "What if vacuum decay occurred",
            "What if the universe started contracting",
            "What if new particles were discovered",
            "What if the laws of physics changed",

            # Biological and Life Scenarios
            "What if life evolved on a gas giant",
            "What if silicon-based life existed",
            "What if life didn't need water",
            "What if photosynthesis used different light",
            "What if life existed in space vacuum",
            "What if intelligence evolved multiple times",
            "What if humans could survive in space",
            "What if we found fossil life on Mars",
            "What if life existed in Jupiter's clouds",
            "What if Titan had complex life",
            "What if life existed around black holes",
            "What if consciousness was universal",
            "What if life could travel between stars",
            "What if evolution worked differently",
            "What if DNA had different bases",

            # Observational and Discovery Scenarios
            "What if we could see dark matter",
            "What if telescopes had perfect resolution",
            "What if we could observe the Big Bang",
            "What if we found Earth's twin planet",
            "What if we detected gravitational waves from aliens",
            "What if we could see inside black holes",
            "What if we discovered new dimensions",
            "What if we found evidence of previous universes",
            "What if we could measure quantum gravity",
            "What if we detected magnetic monopoles",
            "What if we found stable wormholes",
            "What if we discovered time crystals in space",
            "What if we could observe quantum entanglement",
            "What if we found cosmic strings",
            "What if we detected extra dimensions",

            # Future Technology Scenarios
            "What if we could manipulate spacetime",
            "What if we mastered quantum computing",
            "What if we could create pocket universes",
            "What if we developed perfect AI",
            "What if we could reverse aging",
            "What if we could transfer minds",
            "What if we built Dyson spheres",
            "What if we could harvest dark energy",
            "What if we created stable fusion reactors",
            "What if we could control weather perfectly",
            "What if we eliminated all diseases",
            "What if we could live for thousands of years",
            "What if we could communicate instantly across space",
            "What if we could predict the future",
            "What if we could change the past"
        ]

    def _get_audience_question_topics(self) -> List[str]:
        """Generate comprehensive list of audience engagement questions."""
        return [
            # Personal Space Preferences
            "Would you rather visit Mars or the Moon first",
            "Which planet would you most like to explore",
            "Would you take a one-way trip to Mars",
            "Which space mission would you want to join",
            "Would you rather meet aliens or discover new physics",
            "Which astronomical object fascinates you most",
            "Would you live on a space station permanently",
            "Which space technology excites you most",
            "Would you rather explore deep space or deep ocean",
            "Which cosmic phenomenon would you witness",
            "Would you sacrifice Earth comforts for space exploration",
            "Which space discovery would change everything",
            "Would you rather be an astronaut or mission control",
            "Which alien contact scenario seems most likely",
            "Would you trust AI to pilot your spacecraft",

            # Ethical Space Dilemmas
            "Should we terraform Mars if it has microbial life",
            "Is it ethical to mine asteroids for profit",
            "Should space exploration be international or national",
            "Would you support genetic modification for space travel",
            "Should we send messages to potential alien civilizations",
            "Is it right to claim ownership of celestial bodies",
            "Should we prioritize Earth problems over space exploration",
            "Would you support mandatory space colonization",
            "Should we preserve alien life or human expansion",
            "Is it ethical to create artificial gravity",
            "Should space tourism be available to everyone",
            "Would you support military use of space",
            "Should we limit population on space colonies",
            "Is it right to alter human biology for space",
            "Should we share space technology with all nations",

            # Future Predictions and Opinions
            "When do you think humans will reach Mars",
            "Will we find alien life in your lifetime",
            "What will space travel look like in 100 years",
            "Do you think time travel will ever be possible",
            "Will humans ever leave the solar system",
            "What space technology will revolutionize life",
            "Do you believe in parallel universes",
            "Will we solve the dark matter mystery",
            "What will be the next major space discovery",
            "Do you think aliens have visited Earth",
            "Will we ever communicate with alien intelligence",
            "What will happen when Andromeda collides with us",
            "Do you think consciousness exists elsewhere",
            "Will we ever understand quantum gravity",
            "What will end human space exploration",

            # Philosophical Space Questions
            "Does the vastness of space make you feel small",
            "What does it mean to be human in the cosmos",
            "Do you think Earth is special in the universe",
            "How does space exploration change your worldview",
            "What is humanity's purpose in the universe",
            "Do you believe in cosmic destiny",
            "How do you cope with cosmic insignificance",
            "What would alien contact mean for religion",
            "Does the universe have consciousness",
            "What is the meaning of existence in infinite space",
            "How do you find purpose knowing cosmic scales",
            "What would immortality mean for space exploration",
            "Do you think the universe is designed",
            "How does cosmic perspective affect daily life",
            "What is humanity's cosmic responsibility",

            # Scientific Curiosity Questions
            "What space mystery keeps you awake at night",
            "Which physics law would you change",
            "What would you ask an alien civilization",
            "Which cosmic event would you time travel to see",
            "What space experiment would you design",
            "Which scientific instrument would you improve",
            "What space data would revolutionize science",
            "Which cosmic phenomenon needs better explanation",
            "What would prove we're in a simulation",
            "Which space theory do you hope is wrong",
            "What would change your mind about aliens",
            "Which cosmic discovery would scare you most",
            "What space question has no good answer",
            "Which physics breakthrough would change everything",
            "What would you sacrifice to understand dark matter",

            # Practical Space Living
            "What Earth food would you miss most in space",
            "How would you handle isolation on Mars",
            "What Earth activity would you recreate in space",
            "How would you celebrate holidays in space",
            "What would you do with unlimited space resources",
            "How would you design the perfect space habitat",
            "What Earth weather would you miss in space",
            "How would you maintain relationships across space",
            "What would motivate you during long space journeys",
            "How would you handle medical emergencies in space",
            "What entertainment would you need in space",
            "How would you deal with space radiation risks",
            "What would you grow in a space garden",
            "How would you exercise in zero gravity",
            "What would you do if stranded on Mars",

            # Cosmic Perspective Questions
            "How often do you think about your cosmic address",
            "Does knowing about exoplanets change Earth's value",
            "How do you process the age of the universe",
            "What does it mean that we're made of star stuff",
            "How do you relate to cosmic time scales",
            "Does the possibility of multiverses comfort you",
            "How do you handle the concept of infinity",
            "What does it mean to be conscious matter",
            "How do you find meaning in cosmic randomness",
            "Does the anthropic principle make sense to you",
            "How do you cope with cosmic loneliness",
            "What does it mean to observe the universe",
            "How do you process the heat death scenario",
            "Does cosmic evolution give life meaning",
            "How do you relate to your atomic heritage",

            # Technology and Society
            "Should everyone have access to space travel",
            "How would alien contact change human society",
            "What would space colonies do about crime",
            "How would democracy work on Mars",
            "Should space colonies be independent nations",
            "How would economics work in space",
            "What would education look like on Mars",
            "How would art and culture evolve in space",
            "Should we preserve Earth culture in space",
            "How would religion adapt to space colonization",
            "What would sports look like in low gravity",
            "How would we handle conflicts between colonies",
            "Should space colonies have their own languages",
            "How would we maintain human diversity in space",
            "What would family structures look like in space",

            # Personal Cosmic Connection
            "Do you feel connected to the cosmos",
            "What space image moves you most emotionally",
            "How has learning about space changed you",
            "What cosmic fact gives you the most wonder",
            "Do you ever feel like you're traveling through space",
            "What would you tell aliens about humanity",
            "How do you explain your love of space to others",
            "What space experience would complete your life",
            "Do you dream about space exploration",
            "What cosmic realization changed your perspective",
            "How do you share cosmic wonder with children",
            "What space achievement makes you most proud",
            "Do you feel responsible for cosmic stewardship",
            "What would you put in a cosmic time capsule",
            "How do you want to be remembered cosmically"
        ]

    def _get_explainer_topics(self) -> List[str]:
        """Generate comprehensive list of complex space concepts to explain."""
        return [
            # Fundamental Physics Concepts
            "How gravity works and warps spacetime",
            "What is dark matter and why can't we see it",
            "How stars form from gas and dust clouds",
            "What are gravitational waves and how we detect them",
            "How do rockets work in the vacuum of space",
            "What is the speed of light and why it's constant",
            "How does nuclear fusion power the Sun",
            "What causes time dilation near massive objects",
            "How do black holes bend light and time",
            "What is quantum mechanics in simple terms",
            "How does the electromagnetic spectrum work",
            "What are the four fundamental forces",
            "How does matter and antimatter interact",
            "What is entropy and the arrow of time",
            "How do magnetic fields work in space",

            # Astronomical Phenomena
            "How do solar eclipses happen",
            "What causes the phases of the Moon",
            "How do tides work on Earth",
            "What makes auroras appear in the sky",
            "How do meteor showers occur",
            "What causes seasonal changes on Earth",
            "How do planetary rings form and persist",
            "What makes some stars variable in brightness",
            "How do binary star systems work",
            "What causes stellar explosions like supernovas",
            "How do pulsars create regular radio pulses",
            "What makes quasars so incredibly bright",
            "How do galaxy collisions unfold",
            "What causes gamma-ray bursts",
            "How do cosmic rays travel through space",

            # Space Technology and Exploration
            "How do we navigate spacecraft in space",
            "What is orbital mechanics and how satellites stay up",
            "How do space telescopes see different wavelengths",
            "What is the greenhouse effect on planets",
            "How do we measure distances to stars",
            "What is redshift and how it shows expansion",
            "How do we detect exoplanets around other stars",
            "What is the Doppler effect in astronomy",
            "How do radio telescopes work",
            "What is adaptive optics in ground telescopes",
            "How do we communicate with deep space probes",
            "What is aerobraking for spacecraft",
            "How do ion drives propel spacecraft",
            "What is a gravity assist or slingshot maneuver",
            "How do we land rovers on Mars",

            # Cosmology and Universe Structure
            "What is the Big Bang theory",
            "How do we know the universe is expanding",
            "What is cosmic microwave background radiation",
            "How did the first stars and galaxies form",
            "What is dark energy and cosmic acceleration",
            "How do we measure the age of the universe",
            "What is the cosmic web structure",
            "How do galaxy clusters form and evolve",
            "What is nucleosynthesis in the early universe",
            "How do we study the most distant galaxies",
            "What is inflation in the early universe",
            "How do we map dark matter distribution",
            "What are the different types of galaxies",
            "How do supermassive black holes grow",
            "What is the multiverse theory",

            # Planetary Science
            "How do planets form around stars",
            "What makes a planet habitable",
            "How do atmospheres evolve on planets",
            "What causes extreme weather on gas giants",
            "How do moons form around planets",
            "What is tidal locking and its effects",
            "How do we study planetary interiors",
            "What causes volcanic activity on moons",
            "How do planetary magnetic fields work",
            "What is atmospheric escape from planets",
            "How do we analyze exoplanet atmospheres",
            "What causes retrograde rotation on planets",
            "How do impact craters form and persist",
            "What is differentiation in planetary formation",
            "How do we determine planetary compositions",

            # Stellar Evolution and Death
            "How do stars live and die",
            "What determines a star's lifetime",
            "How do white dwarf stars form",
            "What happens when stars run out of fuel",
            "How do neutron stars form from supernovas",
            "What makes some stars become black holes",
            "How do red giant stars expand",
            "What are the different types of supernovas",
            "How do stellar remnants cool over time",
            "What is the chandrasekhar limit",
            "How do stars create heavy elements",
            "What causes stars to pulsate",
            "How do massive stars end their lives",
            "What is stellar nucleosynthesis",
            "How do brown dwarfs differ from stars",

            # Space Environment and Conditions
            "What is the vacuum of space really like",
            "How cold is space and why",
            "What is radiation in space and its dangers",
            "How do cosmic rays affect astronauts",
            "What is the solar wind and its effects",
            "How do magnetic fields protect planets",
            "What is space weather and its impacts",
            "How do micrometeoroids pose threats",
            "What is the interstellar medium",
            "How do shock waves propagate in space",
            "What are the Van Allen radiation belts",
            "How does the heliosphere protect us",
            "What is the termination shock",
            "How do planetary magnetospheres work",
            "What is the bow shock around Earth",

            # Observational Astronomy
            "How do we see objects billions of light-years away",
            "What is parallax and stellar distance measurement",
            "How do we determine stellar temperatures",
            "What is spectroscopy and what it reveals",
            "How do we measure stellar masses",
            "What is photometry in astronomy",
            "How do we study invisible wavelengths",
            "What is interferometry in astronomy",
            "How do we correct for atmospheric distortion",
            "What is the cosmic distance ladder",
            "How do we measure galaxy rotation curves",
            "What is gravitational lensing",
            "How do we detect dark matter indirectly",
            "What is standard candle brightness measurement",
            "How do we study the early universe"
        ]
