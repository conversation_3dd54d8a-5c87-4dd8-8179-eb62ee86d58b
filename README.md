# CosmicFacts - Automated Content Generation and Telegram Posting

An intelligent Python application that automatically generates engaging space-related content using Google's Gemini AI and posts it to Telegram channels. The system follows a weekly content schedule with different types of space content for each day.

## Features

- **Automated Content Generation**: Uses Google's Gemini AI API to create engaging space content
- **Weekly Content Schedule**: Different content types for each day of the week
- **Telegram Integration**: Automatically posts formatted content to Telegram channels
- **Modular Architecture**: Well-organized, maintainable codebase following best practices
- **Error Handling**: Robust error handling with retry logic for API calls
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Flexible Configuration**: Environment-based configuration management
- **Content Formatting**: Optimized formatting for Telegram with HTML support

## Weekly Content Schedule

- **Monday**: Cosmic Fact - A surprising or fun space fact
- **Tuesday**: Space Myth-Buster - Debunk a common space myth
- **Wednesday**: Explainer - Breakdown of a space concept in simple terms
- **Thursday**: Space History - A key moment or figure in space history
- **Friday**: Cosmic Mystery - An unsolved space question or phenomenon
- **Saturday**: What If? Scenario - A speculative space-related idea
- **Sunday**: Audience Question - A thought-provoking question to spark comments

## Project Structure

```
cosmicfacts/
├── src/
│   ├── core/
│   │   ├── content_generator.py    # Gemini AI integration
│   │   ├── telegram_poster.py      # Telegram bot functionality
│   │   └── scheduler.py            # Content scheduling logic
│   ├── utils/
│   │   ├── config.py              # Environment configuration
│   │   ├── logger.py              # Logging setup
│   │   └── formatters.py          # Content formatting utilities
│   ├── data/
│   │   └── prompts/               # JSON prompt files for each day
│   └── tests/                     # Unit tests
├── config/
│   └── settings.py                # Application settings
├── docs/
│   └── content.md                 # Content specifications
├── logs/                          # Application logs (auto-created)
├── app.py                         # Main entry point
├── requirements.txt               # Dependencies
└── README.md                      # This file
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd cosmicfacts
   ```

2. **Create a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**:
   The `.env` file should contain:
   ```env
   # TELEGRAM config
   TELEGRAM_BOT_TOKEN=your_bot_token_here
   TELEGRAM_CHANNEL_ID=your_channel_id_here
   
   # GEMINI config
   GEMINI_KEY=your_gemini_api_key_here
   GEMINI_MODEL=gemini-2.0-flash
   
   # Optional settings
   LOG_LEVEL=INFO
   MAX_RETRIES=3
   RETRY_DELAY=5
   ```

## Usage

### Basic Usage

Run the daily content generation and posting:
```bash
python app.py
```

### Command Line Options

```bash
# Generate and post today's content (default)
python app.py --action daily

# Generate custom content type
python app.py --action custom --content-type cosmic_fact

# Generate custom content with specific prompt
python app.py --action custom --content-type explainer --prompt "Explain black holes in simple terms"

# Test connections to external services
python app.py --action test

# Show the weekly content schedule
python app.py --action schedule
```

### Available Content Types

- `cosmic_fact`
- `myth_buster`
- `explainer`
- `space_history`
- `cosmic_mystery`
- `what_if_scenario`
- `audience_question`

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `TELEGRAM_BOT_TOKEN` | Telegram bot token | Yes |
| `TELEGRAM_CHANNEL_ID` | Telegram channel ID | Yes |
| `GEMINI_KEY` | Google Gemini API key | Yes |
| `GEMINI_MODEL` | Gemini model name | No (default: gemini-2.0-flash) |
| `LOG_LEVEL` | Logging level | No (default: INFO) |
| `MAX_RETRIES` | API retry attempts | No (default: 3) |
| `RETRY_DELAY` | Retry delay in seconds | No (default: 5) |

### Prompt Customization

Prompts for each content type are stored in `src/data/prompts/` as JSON files. You can customize these prompts by editing the respective files:

- `cosmic_fact.json`
- `myth_buster.json`
- `explainer.json`
- `space_history.json`
- `cosmic_mystery.json`
- `what_if_scenario.json`
- `audience_question.json`

## Testing

Run the unit tests:
```bash
pytest src/tests/
```

Run tests with coverage:
```bash
pytest src/tests/ --cov=src --cov-report=html
```

## Logging

The application creates detailed logs in the `logs/` directory:
- Console output with formatted messages
- JSON-formatted log files for structured logging
- Daily log rotation with timestamps

## Error Handling

The application includes comprehensive error handling:
- API retry logic with exponential backoff
- Graceful degradation when services are unavailable
- Detailed error logging for debugging
- Fallback content generation when parsing fails

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the logs in the `logs/` directory
2. Run connection tests: `python app.py --action test`
3. Review the configuration in `.env`
4. Check the GitHub issues page
