"""
Image prompt generator for creating detailed AI image generation prompts.
Generates highly detailed prompts for Tuesday, Wednesday, and Thursday content.
"""

import json
import re
from typing import Dict, Any, Optional
from pathlib import Path

from src.utils.logger import get_logger
from src.utils.config import config
import google.generativeai as genai

logger = get_logger(__name__)


class ImagePromptGenerator:
    """Generate detailed image prompts for AI image generators."""
    
    def __init__(self):
        """Initialize the image prompt generator."""
        self.model_name = config.gemini_model or "gemini-2.0-flash"
        
        # Configure Gemini
        if not config.gemini_api_key:
            raise ValueError("Gemini API key not configured")
        
        genai.configure(api_key=config.gemini_api_key)
        self.model = genai.GenerativeModel(self.model_name)
        
        # Create prompts directory
        self.prompts_dir = Path("src/data/image_prompts")
        self.prompts_dir.mkdir(exist_ok=True)
        
        logger.info("Image prompt generator initialized")
    
    def generate_image_prompt(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate detailed image prompt based on content.
        
        Args:
            content_data: Generated content with title, content, content_type
            
        Returns:
            Dictionary with image prompt and metadata
        """
        try:
            content_type = content_data.get("content_type", "")
            title = content_data.get("title", "")
            content = content_data.get("content", "")
            
            logger.info(f"Generating image prompt for {content_type}: {title}")
            
            # Get content-type specific prompt template
            prompt_template = self._get_prompt_template(content_type)
            
            # Generate image prompt using Gemini
            image_prompt = self._generate_with_gemini(prompt_template, title, content, content_type)
            
            # Save prompt to file
            prompt_file_path = self._save_prompt_to_file(content_data, image_prompt)
            
            return {
                "image_prompt": image_prompt,
                "content_title": title,
                "content_type": content_type,
                "prompt_file_path": str(prompt_file_path),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error generating image prompt: {str(e)}")
            return {
                "error": str(e),
                "success": False
            }
    
    def _get_prompt_template(self, content_type: str) -> str:
        """Get content-type specific prompt template."""
        
        templates = {
            "myth_buster": """
Create a highly detailed image generation prompt for a space myth-busting visual. The image should clearly show the contrast between myth and reality.

Content Title: {title}
Content: {content}

Generate a detailed prompt that includes:
- Split-screen or before/after composition showing myth vs reality
- Scientific accuracy in the "reality" side
- Clear visual metaphors for the misconception
- Space/astronomy theme with realistic cosmic elements
- Professional educational style
- Specific lighting, colors, and composition details
- Technical specifications for AI image generation

Make the prompt very detailed and specific for optimal AI image generation results.
""",
            
            "explainer": """
Create a highly detailed image generation prompt for an educational space diagram/infographic.

Content Title: {title}
Content: {content}

Generate a detailed prompt that includes:
- Clear educational diagram or infographic style
- Step-by-step visual explanation of the concept
- Scientific accuracy and proper proportions
- Labels, arrows, and explanatory elements
- Professional scientific illustration style
- Specific color schemes that aid understanding
- Detailed composition and layout specifications
- Technical specifications for AI image generation

Make the prompt very detailed and specific for optimal AI image generation results.
""",
            
            "space_history": """
Create a highly detailed image generation prompt for a historical space moment visualization.

Content Title: {title}
Content: {content}

Generate a detailed prompt that includes:
- Historical accuracy and period-appropriate details
- Dramatic composition capturing the significance of the moment
- Realistic space equipment, vehicles, or environments from the era
- Proper historical context and setting
- Cinematic lighting and composition
- Specific details about spacecraft, astronauts, or equipment
- Atmospheric and emotional elements
- Technical specifications for AI image generation

Make the prompt very detailed and specific for optimal AI image generation results.
"""
        }
        
        return templates.get(content_type, templates["explainer"])
    
    def _generate_with_gemini(self, template: str, title: str, content: str, content_type: str) -> str:
        """Generate image prompt using Gemini AI."""
        try:
            # Format the template with content
            formatted_prompt = template.format(
                title=title,
                content=content,
                content_type=content_type
            )
            
            # Add additional instructions for detailed prompts
            full_prompt = f"""
{formatted_prompt}

IMPORTANT REQUIREMENTS:
1. Make the prompt extremely detailed and specific
2. Include technical specifications (resolution, aspect ratio, style)
3. Specify exact colors, lighting conditions, and camera angles
4. Include scientific accuracy requirements
5. Mention specific space/astronomy elements that must be included
6. Provide composition and framing details
7. Specify the artistic style (photorealistic, scientific illustration, etc.)
8. Include any text or labels that should appear in the image
9. Make it optimized for AI image generators like DALL-E, Midjourney, or Stable Diffusion
10. Length should be very rich and descriptive (200-400 words)

Generate only the image prompt, no additional commentary.
"""
            
            # Generate with Gemini
            response = self.model.generate_content(full_prompt)
            
            if response and response.text:
                image_prompt = response.text.strip()
                logger.info("Image prompt generated successfully with Gemini")
                return image_prompt
            else:
                logger.error("Empty response from Gemini")
                return self._get_fallback_prompt(content_type, title)
                
        except Exception as e:
            logger.error(f"Error generating with Gemini: {str(e)}")
            return self._get_fallback_prompt(content_type, title)
    
    def _get_fallback_prompt(self, content_type: str, title: str) -> str:
        """Get fallback prompt if Gemini fails."""
        
        fallback_prompts = {
            "myth_buster": f"""
Detailed space myth-busting illustration for "{title}". Split-screen composition showing myth vs reality. Left side: common misconception with dramatic, exaggerated elements. Right side: scientific reality with accurate proportions and realistic space elements. Professional educational style, clean layout, scientific accuracy, realistic cosmic background with stars and nebulae. High resolution, 16:9 aspect ratio, photorealistic style with educational infographic elements. Clear contrast between fantasy and science, proper lighting, deep space colors (blues, purples, blacks), professional scientific illustration quality.
""",
            
            "explainer": f"""
Detailed educational space diagram for "{title}". Professional scientific infographic style, step-by-step visual explanation, clear labels and arrows, cutaway views showing internal structures, accurate proportions and scientific details. Clean white or dark space background, professional color scheme (blues, whites, oranges for highlights), multiple viewing angles, detailed technical illustration style. High resolution, educational poster quality, realistic space elements, proper scientific accuracy, clear typography for labels, 16:9 aspect ratio, professional scientific visualization.
""",
            
            "space_history": f"""
Detailed historical space scene for "{title}". Cinematic composition capturing the historical moment, period-accurate spacecraft and equipment, realistic astronauts in proper suits, dramatic lighting emphasizing the significance, authentic historical details. Photorealistic style, proper historical context, accurate space environment, emotional atmosphere, heroic composition, realistic proportions, authentic colors and materials from the era. High resolution, cinematic aspect ratio, documentary photography style, historically accurate, dramatic lighting, deep space background.
"""
        }
        
        return fallback_prompts.get(content_type, fallback_prompts["explainer"]).strip()
    
    def _save_prompt_to_file(self, content_data: Dict[str, Any], image_prompt: str) -> Path:
        """Save image prompt to a text file."""
        try:
            # Create filename based on content
            title = content_data.get("title", "untitled")
            content_type = content_data.get("content_type", "content")
            
            # Clean title for filename
            clean_title = re.sub(r'[^\w\s-]', '', title)
            clean_title = re.sub(r'[-\s]+', '_', clean_title)
            clean_title = clean_title[:50]  # Limit length
            
            filename = f"{content_type}_{clean_title}_image_prompt.txt"
            file_path = self.prompts_dir / filename
            
            # Create file content
            file_content = f"""IMAGE GENERATION PROMPT
========================

Content Type: {content_data.get('content_type', 'N/A')}
Content Title: {content_data.get('title', 'N/A')}
Generated: {content_data.get('timestamp', 'N/A')}

DETAILED IMAGE PROMPT:
{image_prompt}

========================
Instructions: Use this prompt with AI image generators like DALL-E, Midjourney, or Stable Diffusion.
After generating the image, manually post it to the Telegram channel along with the content.
"""
            
            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_content)
            
            logger.info(f"Image prompt saved to: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Error saving prompt to file: {str(e)}")
            # Return a default path even if saving fails
            return self.prompts_dir / "error_prompt.txt"
    
    def get_prompt_statistics(self) -> Dict[str, Any]:
        """Get statistics about generated prompts."""
        try:
            prompt_files = list(self.prompts_dir.glob("*.txt"))
            
            stats = {
                "total_prompts_generated": len(prompt_files),
                "prompts_directory": str(self.prompts_dir),
                "recent_prompts": []
            }
            
            # Get recent prompts (last 5)
            recent_files = sorted(prompt_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]
            
            for file_path in recent_files:
                stats["recent_prompts"].append({
                    "filename": file_path.name,
                    "size": file_path.stat().st_size,
                    "created": file_path.stat().st_mtime
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting prompt statistics: {str(e)}")
            return {"error": str(e)}


def test_image_prompt_generator():
    """Test the image prompt generator."""
    try:
        generator = ImagePromptGenerator()
        
        # Test content data
        test_content = {
            "title": "Black Holes Don't Suck Everything In",
            "content": "Contrary to popular belief, black holes don't act like cosmic vacuum cleaners...",
            "content_type": "myth_buster",
            "timestamp": "2024-01-01T12:00:00Z"
        }
        
        # Generate prompt
        result = generator.generate_image_prompt(test_content)
        
        if result.get("success"):
            print("✅ Image prompt generated successfully!")
            print(f"Prompt: {result['image_prompt'][:200]}...")
            print(f"File saved: {result['prompt_file_path']}")
        else:
            print(f"❌ Failed to generate prompt: {result.get('error')}")
        
        # Get statistics
        stats = generator.get_prompt_statistics()
        print(f"\nStatistics: {stats}")
        
    except Exception as e:
        print(f"Test failed: {str(e)}")


if __name__ == "__main__":
    test_image_prompt_generator()
